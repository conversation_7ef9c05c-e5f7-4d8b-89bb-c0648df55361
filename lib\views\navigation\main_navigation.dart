import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/user_controller.dart';
import '../../models/user.dart';
import '../../core/app_routes.dart';
import 'bottom_navigation.dart';

// Customer Views
import '../customer/customer_home_screen.dart';
import '../customer/restaurant_list_screen.dart';
import '../customer/cart_screen.dart';
import '../customer/customer_profile_screen.dart';

// Restaurant Views
import '../restaurant/restaurant_dashboard.dart';
import '../restaurant/menu_management_screen.dart';
import '../restaurant/order_management_screen.dart';
import '../restaurant/restaurant_profile_screen.dart';

// Driver Views
import '../driver/driver_dashboard.dart';
import '../driver/driver_earnings_screen.dart';
import '../driver/driver_profile_screen.dart';

// Admin Views
import '../admin/admin_dashboard.dart';
import '../admin/user_management_screen.dart';
import '../admin/analytics_screen.dart';

// Shared Views
import '../shared/loading_widget.dart';

class MainNavigation extends StatefulWidget {
  const MainNavigation({super.key});

  @override
  State<MainNavigation> createState() => _MainNavigationState();
}

class _MainNavigationState extends State<MainNavigation> {
  final UserController _userController = Get.find<UserController>();
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _setInitialIndex();
  }

  void _setInitialIndex() {
    final currentRoute = Get.currentRoute;
    _currentIndex = _getIndexForRoute(currentRoute);
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final user = _userController.currentUser;
      
      if (user == null) {
        return const Scaffold(
          body: LoadingWidget(message: 'Loading user data...'),
        );
      }

      return Scaffold(
        body: _buildBody(user.role),
        bottomNavigationBar: CustomBottomNavigation(
          currentIndex: _currentIndex,
          onTap: _onTabTapped,
        ),
      );
    });
  }

  Widget _buildBody(UserRole role) {
    switch (role) {
      case UserRole.customer:
        return _buildCustomerBody();
      case UserRole.restaurant:
        return _buildRestaurantBody();
      case UserRole.driver:
        return _buildDriverBody();
      case UserRole.admin:
        return _buildAdminBody();
    }
  }

  Widget _buildCustomerBody() {
    switch (_currentIndex) {
      case 0:
        return const CustomerHomeScreen();
      case 1:
        return const RestaurantListScreen();
      case 2:
        return const CartScreen();
      case 3:
        return const CustomerHomeScreen(); // TODO: Create OrdersScreen
      case 4:
        return const CustomerProfileScreen();
      default:
        return const CustomerHomeScreen();
    }
  }

  Widget _buildRestaurantBody() {
    switch (_currentIndex) {
      case 0:
        return const RestaurantDashboard();
      case 1:
        return const MenuManagementScreen();
      case 2:
        return const OrderManagementScreen();
      case 3:
        return const AnalyticsScreen(); // Restaurant analytics
      case 4:
        return const RestaurantProfileScreen();
      default:
        return const RestaurantDashboard();
    }
  }

  Widget _buildDriverBody() {
    switch (_currentIndex) {
      case 0:
        return const DriverDashboard();
      case 1:
        return const DriverDashboard(); // TODO: Create AvailableDeliveriesScreen
      case 2:
        return const DriverEarningsScreen();
      case 3:
        return const DriverProfileScreen();
      default:
        return const DriverDashboard();
    }
  }

  Widget _buildAdminBody() {
    switch (_currentIndex) {
      case 0:
        return const AdminDashboard();
      case 1:
        return const UserManagementScreen();
      case 2:
        return const AnalyticsScreen();
      case 3:
        return const CustomerProfileScreen(); // TODO: Create AdminSettingsScreen
      default:
        return const AdminDashboard();
    }
  }

  void _onTabTapped(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  int _getIndexForRoute(String route) {
    final user = _userController.currentUser;
    if (user == null) return 0;

    switch (user.role) {
      case UserRole.customer:
        return _getCustomerIndex(route);
      case UserRole.restaurant:
        return _getRestaurantIndex(route);
      case UserRole.driver:
        return _getDriverIndex(route);
      case UserRole.admin:
        return _getAdminIndex(route);
    }
  }

  int _getCustomerIndex(String route) {
    switch (route) {
      case AppRoutes.home:
        return 0;
      case AppRoutes.restaurants:
        return 1;
      case AppRoutes.cart:
        return 2;
      case AppRoutes.orders:
        return 3;
      case AppRoutes.profile:
        return 4;
      default:
        return 0;
    }
  }

  int _getRestaurantIndex(String route) {
    switch (route) {
      case AppRoutes.restaurantDashboard:
        return 0;
      case AppRoutes.menuManagement:
        return 1;
      case AppRoutes.orderManagement:
        return 2;
      case AppRoutes.analytics:
        return 3;
      case AppRoutes.restaurantProfile:
        return 4;
      default:
        return 0;
    }
  }

  int _getDriverIndex(String route) {
    switch (route) {
      case AppRoutes.driverDashboard:
        return 0;
      case AppRoutes.driverEarnings:
        return 2;
      case AppRoutes.driverProfile:
        return 3;
      default:
        return 0;
    }
  }

  int _getAdminIndex(String route) {
    switch (route) {
      case AppRoutes.adminDashboard:
        return 0;
      case AppRoutes.userManagement:
        return 1;
      case AppRoutes.analytics:
        return 2;
      case AppRoutes.profile:
        return 3;
      default:
        return 0;
    }
  }
}

// Navigation Helper Class
class NavigationHelper {
  static void navigateBasedOnRole(UserRole role) {
    switch (role) {
      case UserRole.customer:
        Get.offAllNamed(AppRoutes.home);
        break;
      case UserRole.restaurant:
        Get.offAllNamed(AppRoutes.restaurantDashboard);
        break;
      case UserRole.driver:
        Get.offAllNamed(AppRoutes.driverDashboard);
        break;
      case UserRole.admin:
        Get.offAllNamed(AppRoutes.adminDashboard);
        break;
    }
  }

  static String getDefaultRouteForRole(UserRole role) {
    switch (role) {
      case UserRole.customer:
        return AppRoutes.home;
      case UserRole.restaurant:
        return AppRoutes.restaurantDashboard;
      case UserRole.driver:
        return AppRoutes.driverDashboard;
      case UserRole.admin:
        return AppRoutes.adminDashboard;
    }
  }

  static List<String> getRoutesForRole(UserRole role) {
    switch (role) {
      case UserRole.customer:
        return [
          AppRoutes.home,
          AppRoutes.restaurants,
          AppRoutes.cart,
          AppRoutes.orders,
          AppRoutes.profile,
        ];
      case UserRole.restaurant:
        return [
          AppRoutes.restaurantDashboard,
          AppRoutes.menuManagement,
          AppRoutes.orderManagement,
          AppRoutes.analytics,
          AppRoutes.restaurantProfile,
        ];
      case UserRole.driver:
        return [
          AppRoutes.driverDashboard,
          AppRoutes.driverEarnings,
          AppRoutes.driverProfile,
        ];
      case UserRole.admin:
        return [
          AppRoutes.adminDashboard,
          AppRoutes.userManagement,
          AppRoutes.analytics,
          AppRoutes.profile,
        ];
    }
  }

  static bool isRouteAllowedForRole(String route, UserRole role) {
    final allowedRoutes = getRoutesForRole(role);
    return allowedRoutes.contains(route);
  }
}
