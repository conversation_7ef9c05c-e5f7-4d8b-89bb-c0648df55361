import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/restaurant_controller.dart';
import '../../controllers/cart_controller.dart';
import '../../core/app_routes.dart';
import '../../models/menu_item.dart';
import '../shared/custom_app_bar.dart';
import '../shared/custom_cards.dart';
import '../shared/loading_widget.dart';
import '../shared/error_widget.dart';
import '../shared/empty_state_widget.dart';

class MenuScreen extends StatefulWidget {
  const MenuScreen({super.key});

  @override
  State<MenuScreen> createState() => _MenuScreenState();
}

class _MenuScreenState extends State<MenuScreen> {
  final RestaurantController _restaurantController = Get.find<RestaurantController>();
  final CartController _cartController = Get.find<CartController>();

  @override
  void initState() {
    super.initState();
    _loadMenuItems();
  }

  Future<void> _loadMenuItems() async {
    final restaurant = _restaurantController.selectedRestaurant;
    if (restaurant != null) {
      await _restaurantController.loadMenuItems(restaurant.id);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: CustomAppBar(
        title: _restaurantController.selectedRestaurant?.fullName ?? 'Menu',
        actions: [
          Obx(() {
            final itemCount = _cartController.itemCount;
            return Stack(
              children: [
                IconButton(
                  icon: const Icon(Icons.shopping_cart_outlined),
                  onPressed: () => Get.toNamed(AppRoutes.cart),
                ),
                if (itemCount > 0)
                  Positioned(
                    right: 8,
                    top: 8,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 16,
                        minHeight: 16,
                      ),
                      child: Text(
                        '$itemCount',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            );
          }),
        ],
      ),
      body: Column(
        children: [
          // Restaurant Info Header
          _buildRestaurantHeader(),
          // Category Filter
          _buildCategoryFilter(),
          // Menu Items
          Expanded(
            child: _buildMenuItems(),
          ),
        ],
      ),
      floatingActionButton: Obx(() {
        final itemCount = _cartController.itemCount;
        if (itemCount == 0) return const SizedBox.shrink();

        return FloatingActionButton.extended(
          onPressed: () => Get.toNamed(AppRoutes.cart),
          backgroundColor: Theme.of(context).primaryColor,
          icon: const Icon(Icons.shopping_cart, color: Colors.white),
          label: Text(
            'View Cart ($itemCount)',
            style: const TextStyle(color: Colors.white),
          ),
        );
      }),
    );
  }

  Widget _buildRestaurantHeader() {
    final restaurant = _restaurantController.selectedRestaurant;
    if (restaurant == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.restaurant,
                  size: 30,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      restaurant.fullName,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        const Icon(Icons.star, size: 16, color: Colors.orange),
                        const SizedBox(width: 4),
                        Text(
                          restaurant.stats.rate.rating.toStringAsFixed(1),
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                        const SizedBox(width: 16),
                        const Icon(Icons.access_time, size: 16, color: Colors.grey),
                        const SizedBox(width: 4),
                        Text(
                          restaurant.stats.address.estimatedDelivery,
                          style: TextStyle(color: Colors.grey[600]),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryFilter() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      color: Colors.white,
      child: Obx(() {
        final categories = _restaurantController.categories;
        final selectedCategory = _restaurantController.selectedCategory;

        return SizedBox(
          height: 40,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: categories.length,
            itemBuilder: (context, index) {
              final category = categories[index];
              final isSelected = category == selectedCategory;

              return Container(
                margin: const EdgeInsets.only(right: 8),
                child: FilterChip(
                  label: Text(category),
                  selected: isSelected,
                  onSelected: (selected) {
                    _restaurantController.filterByCategory(category);
                  },
                  selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
                  checkmarkColor: Theme.of(context).primaryColor,
                ),
              );
            },
          ),
        );
      }),
    );
  }

  Widget _buildMenuItems() {
    return Obx(() {
      if (_restaurantController.isLoading) {
        return const LoadingWidget(message: 'Loading menu...');
      }

      if (_restaurantController.errorMessage.isNotEmpty) {
        return CustomErrorWidget(
          message: _restaurantController.errorMessage,
          onRetry: _loadMenuItems,
        );
      }

      final menuItems = _restaurantController.filteredMenuItems;
      if (menuItems.isEmpty) {
        return const EmptyStateWidget(
          title: 'No Menu Items',
          message: 'No menu items available for this category.',
          icon: Icons.restaurant_menu,
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: menuItems.length,
        itemBuilder: (context, index) {
          final menuItem = menuItems[index];
          return _buildMenuItemCard(menuItem);
        },
      );
    });
  }

  Widget _buildMenuItemCard(MenuItem menuItem) {
    return CustomCard(
      margin: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          // Menu Item Image
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.fastfood,
              size: 40,
              color: Colors.grey,
            ),
          ),
          const SizedBox(width: 16),
          // Menu Item Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  menuItem.name,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  menuItem.description,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '\$${menuItem.price.toStringAsFixed(2)}',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                    if (menuItem.allergens.isNotEmpty)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.orange[100],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          'Allergens',
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.orange[800],
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
          // Add to Cart Button
          Column(
            children: [
              if (!menuItem.isAvailable)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    'Unavailable',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                )
              else
                SizedBox(
                  width: 80,
                  height: 36,
                  child: ElevatedButton(
                    onPressed: () => _addToCart(menuItem),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.zero,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                    child: const Text(
                      'Add',
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _addToCart(MenuItem menuItem) async {
    final restaurant = _restaurantController.selectedRestaurant;
    if (restaurant == null) return;

    final success = await _cartController.addItem(menuItem);
    if (success) {
      SuccessSnackBar.show(context, '${menuItem.name} added to cart!');
    } else {
      ErrorSnackBar.show(context, _cartController.errorMessage);
    }
  }
}
