import 'dart:convert';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import '../services/demo_data_service.dart';
import '../core/app_routes.dart';

class UserController extends GetxController {
  // Observable variables
  final Rx<UserModel?> _currentUser = Rx<UserModel?>(null);
  final RxBool _isLoading = false.obs;
  final RxBool _isLoggedIn = false.obs;
  final RxString _errorMessage = ''.obs;
  final RxList<UserModel> _allUsers = <UserModel>[].obs;

  // Getters
  UserModel? get currentUser => _currentUser.value;
  bool get isLoading => _isLoading.value;
  bool get isLoggedIn => _isLoggedIn.value;
  String get errorMessage => _errorMessage.value;
  List<UserModel> get allUsers => _allUsers;
  bool get isAdmin => currentUser?.role.isAdmin ?? false;
  bool get isCustomer => currentUser?.role.isCustomer ?? false;
  bool get isRestaurant => currentUser?.role == UserRole.restaurant;
  bool get isDriver => currentUser?.role == UserRole.driver;

  @override
  void onInit() {
    super.onInit();
    _loadUserFromStorage();
    _loadDemoUsers();
  }

  // Authentication methods
  Future<bool> login(String email, String password) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Simulate API call delay
      await Future.delayed(const Duration(seconds: 1));

      // Find user in demo data
      final user = _allUsers.firstWhereOrNull(
        (user) => user.email.toLowerCase() == email.toLowerCase(),
      );

      if (user == null) {
        _errorMessage.value = 'User not found';
        return false;
      }

      // In a real app, you would verify the password here
      // For demo purposes, we'll accept any password
      await _setCurrentUser(user);
      _isLoggedIn.value = true;

      // Navigate based on user role
      _navigateBasedOnRole();

      return true;
    } catch (e) {
      _errorMessage.value = 'Login failed: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> register({
    required String firstName,
    required String lastName,
    required String email,
    required String password,
    String? phone,
    UserRole role = UserRole.customer,
  }) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Check if user already exists
      final existingUser = _allUsers.firstWhereOrNull(
        (user) => user.email.toLowerCase() == email.toLowerCase(),
      );

      if (existingUser != null) {
        _errorMessage.value = 'User with this email already exists';
        return false;
      }

      // Create new user
      final newUser = UserModel(
        id: 'user_${DateTime.now().millisecondsSinceEpoch}',
        firstName: firstName,
        lastName: lastName,
        email: email,
        phone: phone,
        dateJoined: DateTime.now(),
        role: role,
        preferences: UserPreferences(),
        stats: UserStats(
          rate: RateModel(
            id: 'rate_${DateTime.now().millisecondsSinceEpoch}',
            userId: 'user_${DateTime.now().millisecondsSinceEpoch}',
            restaureantId: '',
            rating: 0.0,
            comment: '',
            createdAt: DateTime.now(),
          ),
          address: AddressModel(
            id: 'addr_${DateTime.now().millisecondsSinceEpoch}',
            type: 'Home',
            fullAddress: '',
            unit: null,
            isDefault: true,
            estimatedDelivery: '30-45 mins',
          ),
        ),
      );

      _allUsers.add(newUser);
      await _setCurrentUser(newUser);
      _isLoggedIn.value = true;

      // Navigate based on user role
      _navigateBasedOnRole();

      return true;
    } catch (e) {
      _errorMessage.value = 'Registration failed: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> logout() async {
    try {
      _isLoading.value = true;

      // Clear user data
      _currentUser.value = null;
      _isLoggedIn.value = false;

      // Clear from storage
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('current_user');

      // Navigate to login
      Get.offAllNamed(AppRoutes.login);
    } catch (e) {
      _errorMessage.value = 'Logout failed: ${e.toString()}';
    } finally {
      _isLoading.value = false;
    }
  }

  // Profile management
  Future<bool> updateProfile(UserModel updatedUser) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 500));

      // Update in local list
      final index = _allUsers.indexWhere((user) => user.id == updatedUser.id);
      if (index != -1) {
        _allUsers[index] = updatedUser;
      }

      // Update current user if it's the same user
      if (_currentUser.value?.id == updatedUser.id) {
        await _setCurrentUser(updatedUser);
      }

      return true;
    } catch (e) {
      _errorMessage.value = 'Profile update failed: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> updatePreferences(UserPreferences preferences) async {
    if (_currentUser.value == null) return false;

    final updatedUser = _currentUser.value!.copyWith(preferences: preferences);
    return await updateProfile(updatedUser);
  }

  Future<bool> updateStats(UserStats stats) async {
    if (_currentUser.value == null) return false;

    final updatedUser = _currentUser.value!.copyWith(stats: stats);
    return await updateProfile(updatedUser);
  }

  // User management (for admin)
  Future<List<UserModel>> getAllUsers() async {
    try {
      _isLoading.value = true;

      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 500));

      return _allUsers;
    } catch (e) {
      _errorMessage.value = 'Failed to load users: ${e.toString()}';
      return [];
    } finally {
      _isLoading.value = false;
    }
  }

  Future<List<UserModel>> getUsersByRole(UserRole role) async {
    final allUsers = await getAllUsers();
    return allUsers.where((user) => user.role == role).toList();
  }

  Future<bool> deleteUser(String userId) async {
    if (!isAdmin) {
      _errorMessage.value = 'Unauthorized: Admin access required';
      return false;
    }

    try {
      _isLoading.value = true;

      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 500));

      _allUsers.removeWhere((user) => user.id == userId);
      return true;
    } catch (e) {
      _errorMessage.value = 'Failed to delete user: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // Private methods
  Future<void> _setCurrentUser(UserModel user) async {
    _currentUser.value = user;

    // Save to storage
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('current_user', jsonEncode(user.toJson()));
  }

  Future<void> _loadUserFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString('current_user');

      if (userJson != null) {
        final userData = jsonDecode(userJson);
        _currentUser.value = UserModel.fromJson(userData);
        _isLoggedIn.value = true;
      }
    } catch (e) {
      Get.showSnackbar(
        GetSnackBar(
          message: 'Failed to load user: ${e.toString()}',
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  void _navigateBasedOnRole() {
    switch (currentUser?.role) {
      case UserRole.admin:
        Get.offAllNamed(AppRoutes.admin);
        break;
      case UserRole.restaurant:
        Get.offAllNamed(AppRoutes.restaurant);
        break;
      case UserRole.driver:
        Get.offAllNamed(AppRoutes.driver);
        break;
      case UserRole.customer:
      default:
        Get.offAllNamed(AppRoutes.home);
        break;
    }
  }

  Future<void> _loadDemoUsers() async {
    try {
      // Load demo data from JSON file
      await DemoDataService.instance.loadDemoData();
      final users = DemoDataService.instance.getUsers();
      _allUsers.addAll(users);
    } catch (e) {
      // Fallback to hardcoded demo users if JSON loading fails
      _allUsers.addAll([
        UserModel(
          id: 'demo_admin',
          firstName: 'Admin',
          lastName: 'User',
          email: '<EMAIL>',
          dateJoined: DateTime.now().subtract(const Duration(days: 30)),
          role: UserRole.admin,
          isEmailVerified: true,
          preferences: UserPreferences(),
          stats: UserStats(
            rate: RateModel(
              id: 'rate_admin',
              userId: 'demo_admin',
              restaureantId: '',
              rating: 0.0,
              comment: '',
              createdAt: DateTime.now(),
            ),
            address: AddressModel(
              id: 'addr_admin',
              type: 'Office',
              fullAddress: '123 Admin Street, City',
              unit: null,
              isDefault: true,
              estimatedDelivery: '15-25 mins',
            ),
          ),
        ),
        UserModel(
          id: 'demo_customer',
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          dateJoined: DateTime.now().subtract(const Duration(days: 15)),
          role: UserRole.customer,
          isEmailVerified: true,
          preferences: UserPreferences(),
          stats: UserStats(
            totalOrders: 5,
            totalSpent: 125.50,
            rate: RateModel(
              id: 'rate_customer',
              userId: 'demo_customer',
              restaureantId: '',
              rating: 4.5,
              comment: 'Great service!',
              createdAt: DateTime.now(),
            ),
            address: AddressModel(
              id: 'addr_customer',
              type: 'Home',
              fullAddress: '456 Customer Ave, City',
              unit: null,
              isDefault: true,
              estimatedDelivery: '25-35 mins',
            ),
          ),
        ),
      ]);
    }
  }

  // Utility methods
  void clearError() {
    _errorMessage.value = '';
  }

  bool hasPermission(String permission) {
    // Define permissions based on user role
    switch (currentUser?.role) {
      case UserRole.admin:
        return true; // Admin has all permissions
      case UserRole.restaurant:
        return ['manage_menu', 'view_orders', 'update_order_status'].contains(permission);
      case UserRole.driver:
        return ['view_deliveries', 'update_delivery_status'].contains(permission);
      case UserRole.customer:
        return ['place_order', 'view_own_orders', 'rate_restaurant'].contains(permission);
      default:
        return false;
    }
  }
}
