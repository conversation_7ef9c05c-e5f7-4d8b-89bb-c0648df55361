import 'dart:async';
import 'package:get/get.dart';
import '../models/user.dart';
import '../models/order.dart';
import '../services/demo_data_service.dart';
import 'user_controller.dart';
import 'restaurant_controller.dart';
import 'order_controller.dart';

class AdminController extends GetxController {
  // Observable variables
  final RxList<UserModel> _allUsers = <UserModel>[].obs;
  final RxList<UserModel> _allRestaurants = <UserModel>[].obs;
  final RxList<OrderModel> _allOrders = <OrderModel>[].obs;
  final RxBool _isLoading = false.obs;
  final RxString _errorMessage = ''.obs;
  final Rx<AdminStats> _adminStats = AdminStats.empty().obs;
  final RxString _selectedTimeRange = 'today'.obs;

  // Dependencies
  final UserController _userController = Get.find<UserController>();
  final RestaurantController _restaurantController = Get.find<RestaurantController>();
  final OrderController _orderController = Get.find<OrderController>();

  // Getters
  List<UserModel> get allUsers => _allUsers;
  List<UserModel> get allRestaurants => _allRestaurants;
  List<OrderModel> get allOrders => _allOrders;
  bool get isLoading => _isLoading.value;
  String get errorMessage => _errorMessage.value;
  AdminStats get adminStats => _adminStats.value;
  String get selectedTimeRange => _selectedTimeRange.value;

  bool get isAdmin => _userController.currentUser?.role == UserRole.admin;
  bool get canAccessAdminPanel => isAdmin;

  @override
  void onInit() {
    super.onInit();
    if (canAccessAdminPanel) {
      loadAdminData();
    }
    _setupDataListeners();
  }

  // Data loading
  Future<void> loadAdminData() async {
    if (!canAccessAdminPanel) {
      _errorMessage.value = 'Access denied: Admin privileges required';
      return;
    }

    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      await Future.wait([
        _loadAllUsers(),
        _loadAllRestaurants(),
        _loadAllOrders(),
      ]);

      _calculateAdminStats();
    } catch (e) {
      _errorMessage.value = 'Failed to load admin data: ${e.toString()}';
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> _loadAllUsers() async {
    try {
      // Load users from demo data service
      await DemoDataService.instance.loadDemoData();
      final users = DemoDataService.instance.getUsers();

      _allUsers.clear();
      _allUsers.addAll(users);

      // Add current user if not already in the list
      if (_userController.currentUser != null) {
        final currentUserId = _userController.currentUser!.id;
        final userExists = _allUsers.any((user) => user.id == currentUserId);
        if (!userExists) {
          _allUsers.add(_userController.currentUser!);
        }
      }
    } catch (e) {
      // Fallback to hardcoded demo users if JSON loading fails
      _allUsers.clear();

      if (_userController.currentUser != null) {
        _allUsers.add(_userController.currentUser!);
      }

      // Add fallback demo users
      _allUsers.addAll([
        UserModel(
          id: 'user_001',
          firstName: 'John',
          lastName: 'Customer',
          email: '<EMAIL>',
          phone: '+1234567890',
          dateJoined: DateTime.now().subtract(const Duration(days: 30)),
          role: UserRole.customer,
          isActive: true,
          preferences: UserPreferences(),
          stats: UserStats(
            rate: RateModel(
              id: 'rate_user_001',
              userId: 'user_001',
              restaureantId: '',
              rating: 4.5,
              comment: 'Good customer',
              createdAt: DateTime.now(),
            ),
            address: AddressModel(
              id: 'addr_user_001',
              type: 'Home',
              fullAddress: '123 Main St, New York, NY 10001',
              unit: null,
              isDefault: true,
              estimatedDelivery: '30-45 mins',
            ),
          ),
        ),
      ]);
    }
  }

  Future<void> _loadAllRestaurants() async {
    // Get restaurants from restaurant controller
    _allRestaurants.clear();
    _allRestaurants.addAll(_restaurantController.restaurants);
  }

  Future<void> _loadAllOrders() async {
    // Get orders from order controller
    _allOrders.clear();
    _allOrders.addAll(_orderController.orders);
  }

  // User management
  Future<bool> updateUserStatus(String userId, bool isActive) async {
    if (!canAccessAdminPanel) return false;

    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 500));

      final userIndex = _allUsers.indexWhere((user) => user.id == userId);
      if (userIndex == -1) {
        _errorMessage.value = 'User not found';
        return false;
      }

      _allUsers[userIndex] = _allUsers[userIndex].copyWith(isActive: isActive);
      _calculateAdminStats();

      return true;
    } catch (e) {
      _errorMessage.value = 'Failed to update user status: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> updateUserRole(String userId, UserRole newRole) async {
    if (!canAccessAdminPanel) return false;

    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 500));

      final userIndex = _allUsers.indexWhere((user) => user.id == userId);
      if (userIndex == -1) {
        _errorMessage.value = 'User not found';
        return false;
      }

      _allUsers[userIndex] = _allUsers[userIndex].copyWith(role: newRole);
      _calculateAdminStats();

      return true;
    } catch (e) {
      _errorMessage.value = 'Failed to update user role: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> deleteUser(String userId) async {
    if (!canAccessAdminPanel) return false;

    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Don't allow deletion of current admin user
      if (userId == _userController.currentUser?.id) {
        _errorMessage.value = 'Cannot delete your own account';
        return false;
      }

      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 500));

      _allUsers.removeWhere((user) => user.id == userId);
      _calculateAdminStats();

      return true;
    } catch (e) {
      _errorMessage.value = 'Failed to delete user: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // Restaurant management
  Future<bool> approveRestaurant(String restaurantId) async {
    if (!canAccessAdminPanel) return false;

    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 500));

      final restaurantIndex = _allRestaurants.indexWhere((r) => r.id == restaurantId);
      if (restaurantIndex == -1) {
        _errorMessage.value = 'Restaurant not found';
        return false;
      }

      _allRestaurants[restaurantIndex] = _allRestaurants[restaurantIndex].copyWith(isActive: true);
      _calculateAdminStats();

      return true;
    } catch (e) {
      _errorMessage.value = 'Failed to approve restaurant: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> suspendRestaurant(String restaurantId, String reason) async {
    if (!canAccessAdminPanel) return false;

    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 500));

      final restaurantIndex = _allRestaurants.indexWhere((r) => r.id == restaurantId);
      if (restaurantIndex == -1) {
        _errorMessage.value = 'Restaurant not found';
        return false;
      }

      _allRestaurants[restaurantIndex] = _allRestaurants[restaurantIndex].copyWith(isActive: false);
      _calculateAdminStats();

      return true;
    } catch (e) {
      _errorMessage.value = 'Failed to suspend restaurant: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // Order management
  Future<bool> refundOrder(String orderId, String reason) async {
    if (!canAccessAdminPanel) return false;

    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 500));

      final success = await _orderController.updateOrderStatus(orderId, OrderStatus.cancelled);
      if (success) {
        _calculateAdminStats();
      }

      return success;
    } catch (e) {
      _errorMessage.value = 'Failed to refund order: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // Analytics and reporting
  void setTimeRange(String timeRange) {
    _selectedTimeRange.value = timeRange;
    _calculateAdminStats();
  }

  void _calculateAdminStats() {
    final now = DateTime.now();
    DateTime startDate;

    switch (_selectedTimeRange.value) {
      case 'today':
        startDate = DateTime(now.year, now.month, now.day);
        break;
      case 'week':
        startDate = now.subtract(const Duration(days: 7));
        break;
      case 'month':
        startDate = DateTime(now.year, now.month, 1);
        break;
      case 'year':
        startDate = DateTime(now.year, 1, 1);
        break;
      default:
        startDate = DateTime(now.year, now.month, now.day);
    }

    // Filter orders by date range
    final filteredOrders = _allOrders.where((order) => order.placedAt.isAfter(startDate)).toList();

    // Calculate stats
    final totalOrders = filteredOrders.length;
    final totalRevenue = filteredOrders.fold(0.0, (sum, order) => sum + _calculateOrderTotal(order));
    final activeUsers = _allUsers.where((user) => user.isActive).length;
    final activeRestaurants = _allRestaurants.where((r) => r.isActive).length;
    final pendingOrders = filteredOrders
        .where((order) => order.status == OrderStatus.placed || order.status == OrderStatus.confirmed)
        .length;
    final completedOrders = filteredOrders.where((order) => order.status == OrderStatus.delivered).length;
    final cancelledOrders = filteredOrders.where((order) => order.status == OrderStatus.cancelled).length;

    _adminStats.value = AdminStats(
      totalOrders: totalOrders,
      totalRevenue: totalRevenue,
      activeUsers: activeUsers,
      activeRestaurants: activeRestaurants,
      pendingOrders: pendingOrders,
      completedOrders: completedOrders,
      cancelledOrders: cancelledOrders,
      averageOrderValue: totalOrders > 0 ? totalRevenue / totalOrders : 0.0,
    );
  }

  double _calculateOrderTotal(OrderModel order) {
    final itemsTotal = order.items.fold(0.0, (sum, item) => sum + (item.menuItem.price * item.quantity));
    return itemsTotal + order.deliveryFee;
  }

  // Data filtering and search
  List<UserModel> searchUsers(String query) {
    if (query.isEmpty) return _allUsers;

    final lowercaseQuery = query.toLowerCase();
    return _allUsers
        .where((user) =>
            user.firstName.toLowerCase().contains(lowercaseQuery) ||
            user.lastName.toLowerCase().contains(lowercaseQuery) ||
            user.email.toLowerCase().contains(lowercaseQuery) ||
            (user.phone?.contains(query) ?? false))
        .toList();
  }

  List<UserModel> searchRestaurants(String query) {
    if (query.isEmpty) return _allRestaurants;

    final lowercaseQuery = query.toLowerCase();
    return _allRestaurants
        .where((restaurant) =>
            restaurant.firstName.toLowerCase().contains(lowercaseQuery) ||
            restaurant.lastName.toLowerCase().contains(lowercaseQuery) ||
            restaurant.email.toLowerCase().contains(lowercaseQuery))
        .toList();
  }

  List<OrderModel> searchOrders(String query) {
    if (query.isEmpty) return _allOrders;

    return _allOrders.where((order) => order.id.contains(query) || order.restaurantId.contains(query)).toList();
  }

  List<UserModel> getUsersByRole(UserRole role) {
    return _allUsers.where((user) => user.role == role).toList();
  }

  List<OrderModel> getOrdersByStatus(OrderStatus status) {
    return _allOrders.where((order) => order.status == status).toList();
  }

  // Private methods
  void _setupDataListeners() {
    // Note: In a real app, you would set up proper listeners
    // For now, we'll refresh data periodically
    Timer.periodic(const Duration(minutes: 1), (timer) {
      if (canAccessAdminPanel) {
        _loadAllRestaurants();
        _loadAllOrders();
        _calculateAdminStats();
      }
    });
  }

  // Utility methods
  void clearError() {
    _errorMessage.value = '';
  }

  String formatCurrency(double amount) {
    return '\$${amount.toStringAsFixed(2)}';
  }

  String getUserRoleDisplayName(UserRole role) {
    switch (role) {
      case UserRole.customer:
        return 'Customer';
      case UserRole.restaurant:
        return 'Restaurant Owner';
      case UserRole.driver:
        return 'Driver';
      case UserRole.admin:
        return 'Administrator';
    }
  }

  String getOrderStatusDisplayName(OrderStatus status) {
    switch (status) {
      case OrderStatus.placed:
        return 'Placed';
      case OrderStatus.confirmed:
        return 'Confirmed';
      case OrderStatus.preparing:
        return 'Preparing';
      case OrderStatus.ready:
        return 'Ready';
      case OrderStatus.pickedUp:
        return 'Picked Up';
      case OrderStatus.onTheWay:
        return 'On The Way';
      case OrderStatus.delivered:
        return 'Delivered';
      case OrderStatus.cancelled:
        return 'Cancelled';
    }
  }
}

class AdminStats {
  final int totalOrders;
  final double totalRevenue;
  final int activeUsers;
  final int activeRestaurants;
  final int pendingOrders;
  final int completedOrders;
  final int cancelledOrders;
  final double averageOrderValue;

  AdminStats({
    required this.totalOrders,
    required this.totalRevenue,
    required this.activeUsers,
    required this.activeRestaurants,
    required this.pendingOrders,
    required this.completedOrders,
    required this.cancelledOrders,
    required this.averageOrderValue,
  });

  factory AdminStats.empty() {
    return AdminStats(
      totalOrders: 0,
      totalRevenue: 0.0,
      activeUsers: 0,
      activeRestaurants: 0,
      pendingOrders: 0,
      completedOrders: 0,
      cancelledOrders: 0,
      averageOrderValue: 0.0,
    );
  }
}
