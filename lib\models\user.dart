enum UserRole { customer, admin, restaurant, driver }

extension UserRoleExtension on UserRole {
  bool get isAdmin => this != UserRole.customer;
  bool get isCustomer => this == UserRole.customer;

  String get displayName {
    switch (this) {
      case UserRole.customer:
        return 'Customer';
      case UserRole.admin:
        return 'Administrator';
      case UserRole.restaurant:
        return 'Restaurant';
      case UserRole.driver:
        return 'Driver';
    }
  }
}

class UserModel {
  final String id;
  final String firstName;
  final String lastName;
  final String email;
  final String? phone;
  final String? profileImageUrl;
  final DateTime dateJoined;
  final bool isEmailVerified;
  final bool isPhoneVerified;
  final UserRole role;
  final UserPreferences preferences;
  final UserStats stats;

  // Admin-specific fields (only used when role.isAdmin)
  final DateTime? lastLoginAt;
  final bool isActive;
  final String? department;
  final String? notes;

  UserModel({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.email,
    this.phone,
    this.profileImageUrl,
    required this.dateJoined,
    this.isEmailVerified = false,
    this.isPhoneVerified = false,
    this.role = UserRole.customer,
    required this.preferences,
    required this.stats,
    this.lastLoginAt,
    this.isActive = true,
    this.department,
    this.notes,
  });

  String get fullName => '$firstName $lastName';

  UserModel copyWith({
    String? id,
    String? firstName,
    String? lastName,
    String? email,
    String? phone,
    String? profileImageUrl,
    DateTime? dateJoined,
    bool? isEmailVerified,
    bool? isPhoneVerified,
    UserRole? role,
    UserPreferences? preferences,
    UserStats? stats,
    DateTime? lastLoginAt,
    bool? isActive,
    String? department,
    String? notes,
  }) =>
      UserModel(
        id: id ?? this.id,
        firstName: firstName ?? this.firstName,
        lastName: lastName ?? this.lastName,
        email: email ?? this.email,
        phone: phone ?? this.phone,
        profileImageUrl: profileImageUrl ?? this.profileImageUrl,
        dateJoined: dateJoined ?? this.dateJoined,
        isEmailVerified: isEmailVerified ?? this.isEmailVerified,
        isPhoneVerified: isPhoneVerified ?? this.isPhoneVerified,
        role: role ?? this.role,
        preferences: preferences ?? this.preferences,
        stats: stats ?? this.stats,
        lastLoginAt: lastLoginAt ?? this.lastLoginAt,
        isActive: isActive ?? this.isActive,
        department: department ?? this.department,
        notes: notes ?? this.notes,
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'firstName': firstName,
        'lastName': lastName,
        'email': email,
        'phone': phone,
        'profileImageUrl': profileImageUrl,
        'dateJoined': dateJoined.toIso8601String(),
        'isEmailVerified': isEmailVerified,
        'isPhoneVerified': isPhoneVerified,
        'role': role.name,
        'preferences': preferences.toJson(),
        'stats': stats.toJson(),
        'lastLoginAt': lastLoginAt?.toIso8601String(),
        'isActive': isActive,
        'department': department,
        'notes': notes,
      };

  factory UserModel.fromJson(Map<String, dynamic> json) => UserModel(
        id: json['id'],
        firstName: json['firstName'],
        lastName: json['lastName'],
        email: json['email'],
        phone: json['phone'],
        profileImageUrl: json['profileImageUrl'],
        dateJoined: DateTime.parse(json['dateJoined']),
        isEmailVerified: json['isEmailVerified'] ?? false,
        isPhoneVerified: json['isPhoneVerified'] ?? false,
        role: UserRole.values.firstWhere(
          (r) => r.name == json['role'],
          orElse: () => UserRole.customer,
        ),
        preferences: UserPreferences.fromJson(json['preferences']),
        stats: UserStats.fromJson(json['stats']),
        lastLoginAt: json['lastLoginAt'] != null ? DateTime.parse(json['lastLoginAt']) : null,
        isActive: json['isActive'] ?? true,
        department: json['department'],
        notes: json['notes'],
      );
}

class UserPreferences {
  final bool pushNotifications;
  final bool emailNotifications;
  final bool smsNotifications;
  final bool orderUpdates;
  final bool promotionalOffers;
  final String language;
  final String currency;
  final bool darkMode;

  UserPreferences({
    this.pushNotifications = true,
    this.emailNotifications = true,
    this.smsNotifications = false,
    this.orderUpdates = true,
    this.promotionalOffers = true,
    this.language = 'en',
    this.currency = 'USD',
    this.darkMode = false,
  });

  UserPreferences copyWith({
    bool? pushNotifications,
    bool? emailNotifications,
    bool? smsNotifications,
    bool? orderUpdates,
    bool? promotionalOffers,
    String? language,
    String? currency,
    bool? darkMode,
  }) =>
      UserPreferences(
        pushNotifications: pushNotifications ?? this.pushNotifications,
        emailNotifications: emailNotifications ?? this.emailNotifications,
        smsNotifications: smsNotifications ?? this.smsNotifications,
        orderUpdates: orderUpdates ?? this.orderUpdates,
        promotionalOffers: promotionalOffers ?? this.promotionalOffers,
        language: language ?? this.language,
        currency: currency ?? this.currency,
        darkMode: darkMode ?? this.darkMode,
      );

  Map<String, dynamic> toJson() => {
        'pushNotifications': pushNotifications,
        'emailNotifications': emailNotifications,
        'smsNotifications': smsNotifications,
        'orderUpdates': orderUpdates,
        'promotionalOffers': promotionalOffers,
        'language': language,
        'currency': currency,
        'darkMode': darkMode,
      };

  factory UserPreferences.fromJson(Map<String, dynamic> json) => UserPreferences(
        pushNotifications: json['pushNotifications'] ?? true,
        emailNotifications: json['emailNotifications'] ?? true,
        smsNotifications: json['smsNotifications'] ?? false,
        orderUpdates: json['orderUpdates'] ?? true,
        promotionalOffers: json['promotionalOffers'] ?? true,
        language: json['language'] ?? 'en',
        currency: json['currency'] ?? 'USD',
        darkMode: json['darkMode'] ?? false,
      );
}

class UserStats {
  final int totalOrders;
  final double totalSpent;
  final int favoriteRestaurants;
  final RateModel rate;
  final DateTime? lastOrderDate;
  final AddressModel address;

  UserStats({
    this.totalOrders = 0,
    this.totalSpent = 0.0,
    this.favoriteRestaurants = 0,
    required this.rate,
    this.lastOrderDate,
    required this.address,
  });

  UserStats copyWith({
    int? totalOrders,
    double? totalSpent,
    int? favoriteRestaurants,
    RateModel? rate,
    DateTime? lastOrderDate,
    AddressModel? address,
  }) =>
      UserStats(
        totalOrders: totalOrders ?? this.totalOrders,
        totalSpent: totalSpent ?? this.totalSpent,
        favoriteRestaurants: favoriteRestaurants ?? this.favoriteRestaurants,
        rate: rate ?? this.rate,
        lastOrderDate: lastOrderDate ?? this.lastOrderDate,
        address: address ?? this.address,
      );

  Map<String, dynamic> toJson() => {
        'totalOrders': totalOrders,
        'totalSpent': totalSpent,
        'favoriteRestaurants': favoriteRestaurants,
        'rate': rate.toJson(),
        'lastOrderDate': lastOrderDate?.toIso8601String(),
        'address': address.toJson(),
      };

  factory UserStats.fromJson(Map<String, dynamic> json) => UserStats(
        totalOrders: json['totalOrders'] ?? 0,
        totalSpent: (json['totalSpent'] ?? 0.0).toDouble(),
        favoriteRestaurants: json['favoriteRestaurants'] ?? 0,
        rate: RateModel.fromJson(json['rate']),
        lastOrderDate: json['lastOrderDate'] != null ? DateTime.parse(json['lastOrderDate']) : null,
        address: AddressModel.fromJson(json['address']),
      );
}

class RateModel {
  final String id;
  final String userId;
  final String restaureantId;
  final double rating;
  final String comment;
  final DateTime createdAt;

  RateModel({
    required this.id,
    required this.userId,
    required this.restaureantId,
    required this.rating,
    required this.comment,
    required this.createdAt,
  });

  factory RateModel.fromJson(Map<String, dynamic> json) => RateModel(
        id: json['id'],
        userId: json['userId'],
        restaureantId: json['restaureantId'],
        rating: json['rating'],
        comment: json['comment'],
        createdAt: DateTime.parse(json['createdAt']),
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'userId': userId,
        'restaureantId': restaureantId,
        'rating': rating,
        'comment': comment,
        'createdAt': createdAt.toIso8601String(),
      };
}

class AddressModel {
  final String id;
  final String type;
  final String fullAddress;
  final String? unit;
  final bool isDefault;
  final String estimatedDelivery;

  AddressModel({
    required this.id,
    required this.type,
    required this.fullAddress,
    required this.unit,
    required this.isDefault,
    required this.estimatedDelivery,
  });

  factory AddressModel.fromJson(Map<String, dynamic> json) => AddressModel(
        id: json['id'],
        type: json['type'],
        fullAddress: json['fullAddress'],
        unit: json['unit'],
        isDefault: json['isDefault'],
        estimatedDelivery: json['estimatedDelivery'],
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'type': type,
        'fullAddress': fullAddress,
        'unit': unit,
        'isDefault': isDefault,
        'estimatedDelivery': estimatedDelivery,
      };
}
