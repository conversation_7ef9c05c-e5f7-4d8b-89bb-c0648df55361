import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/restaurant_controller.dart';
import '../../models/user.dart';
import '../../models/menu_item.dart';
import '../shared/custom_app_bar.dart';
import '../shared/custom_cards.dart';
import '../shared/loading_widget.dart';
import '../shared/error_widget.dart';
import '../shared/empty_state_widget.dart';
import '../shared/snackbars.dart';

class SearchResultsScreen extends StatefulWidget {
  const SearchResultsScreen({super.key});

  @override
  State<SearchResultsScreen> createState() => _SearchResultsScreenState();
}

class _SearchResultsScreenState extends State<SearchResultsScreen> {
  final RestaurantController _restaurantController = Get.find<RestaurantController>();
  final TextEditingController _searchController = TextEditingController();
  
  String _currentQuery = '';
  List<UserModel> _restaurantResults = [];
  List<MenuItem> _menuItemResults = [];
  bool _isLoading = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    // Get the search query from arguments
    final arguments = Get.arguments as Map<String, dynamic>?;
    _currentQuery = arguments?['query'] ?? '';
    _searchController.text = _currentQuery;
    
    if (_currentQuery.isNotEmpty) {
      _performSearch(_currentQuery);
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _performSearch(String query) async {
    if (query.trim().isEmpty) return;

    setState(() {
      _isLoading = true;
      _errorMessage = '';
      _currentQuery = query.trim();
    });

    try {
      // Search restaurants
      final restaurants = _restaurantController.restaurants;
      _restaurantResults = restaurants.where((restaurant) {
        final name = '${restaurant.firstName} ${restaurant.lastName}'.toLowerCase();
        final email = restaurant.email.toLowerCase();
        final address = restaurant.stats.address.fullAddress.toLowerCase();
        final searchTerm = query.toLowerCase();
        
        return name.contains(searchTerm) || 
               email.contains(searchTerm) || 
               address.contains(searchTerm);
      }).toList();

      // Search menu items across all restaurants
      _menuItemResults = [];
      for (final restaurant in restaurants) {
        // Load menu items for each restaurant
        await _restaurantController.selectRestaurant(restaurant);
        await _restaurantController.loadMenuItems(restaurant.id);
        
        final menuItems = _restaurantController.allMenuItems.where((item) {
          final itemName = item.name.toLowerCase();
          final itemDescription = item.description.toLowerCase();
          final itemCategory = item.category.toLowerCase();
          final searchTerm = query.toLowerCase();
          
          return itemName.contains(searchTerm) || 
                 itemDescription.contains(searchTerm) || 
                 itemCategory.contains(searchTerm);
        }).toList();
        
        _menuItemResults.addAll(menuItems);
      }

    } catch (e) {
      setState(() {
        _errorMessage = 'Search failed: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: CustomAppBar(
        title: 'Search Results',
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Column(
        children: [
          // Search bar
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search for restaurants or food...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    setState(() {
                      _restaurantResults.clear();
                      _menuItemResults.clear();
                      _currentQuery = '';
                    });
                  },
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey[100],
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 12,
                ),
              ),
              onSubmitted: _performSearch,
            ),
          ),
          
          // Results
          Expanded(
            child: _buildResults(),
          ),
        ],
      ),
    );
  }

  Widget _buildResults() {
    if (_isLoading) {
      return const LoadingWidget(message: 'Searching...');
    }

    if (_errorMessage.isNotEmpty) {
      return CustomErrorWidget(
        message: _errorMessage,
        onRetry: () => _performSearch(_currentQuery),
      );
    }

    if (_currentQuery.isEmpty) {
      return const EmptyStateWidget(
        icon: Icons.search,
        title: 'Start Searching',
        message: 'Enter a search term to find restaurants and menu items.',
      );
    }

    final hasResults = _restaurantResults.isNotEmpty || _menuItemResults.isNotEmpty;

    if (!hasResults) {
      return EmptyStateWidget(
        icon: Icons.search_off,
        title: 'No Results Found',
        message: 'No restaurants or menu items found for "$_currentQuery".',
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Search summary
          Text(
            'Results for "$_currentQuery"',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '${_restaurantResults.length} restaurant${_restaurantResults.length != 1 ? 's' : ''}, '
            '${_menuItemResults.length} menu item${_menuItemResults.length != 1 ? 's' : ''}',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),

          // Restaurant results
          if (_restaurantResults.isNotEmpty) ...[
            Text(
              'Restaurants',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            ...(_restaurantResults.map((restaurant) => _buildRestaurantCard(restaurant))),
            const SizedBox(height: 24),
          ],

          // Menu item results
          if (_menuItemResults.isNotEmpty) ...[
            Text(
              'Menu Items',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            ...(_menuItemResults.map((menuItem) => _buildMenuItemCard(menuItem))),
          ],
        ],
      ),
    );
  }

  Widget _buildRestaurantCard(UserModel restaurant) {
    final rating = restaurant.stats.rate.rating;
    final address = restaurant.stats.address.fullAddress;
    final estimatedDelivery = restaurant.stats.address.estimatedDelivery;

    return CustomCard(
      margin: const EdgeInsets.only(bottom: 12),
      onTap: () {
        // Navigate to restaurant menu
        Get.toNamed('/menu', arguments: {'restaurant': restaurant});
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              // Restaurant image placeholder
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.restaurant,
                  color: Colors.grey,
                  size: 30,
                ),
              ),
              const SizedBox(width: 12),
              
              // Restaurant info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${restaurant.firstName} ${restaurant.lastName}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.star,
                          size: 16,
                          color: Colors.amber[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          rating.toStringAsFixed(1),
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Icon(
                          Icons.access_time,
                          size: 16,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          estimatedDelivery,
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      address,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItemCard(MenuItem menuItem) {
    return CustomCard(
      margin: const EdgeInsets.only(bottom: 12),
      onTap: () {
        // Find the restaurant for this menu item
        final restaurant = _findRestaurantForMenuItem(menuItem.id);
        if (restaurant != null) {
          Get.toNamed('/menu', arguments: {
            'restaurant': restaurant,
            'highlightItem': menuItem.id,
          });
        }
      },
      child: Row(
        children: [
          // Menu item image placeholder
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.fastfood,
              color: Colors.grey,
              size: 30,
            ),
          ),
          const SizedBox(width: 12),
          
          // Menu item info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  menuItem.name,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  menuItem.description,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      '\$${menuItem.price.toStringAsFixed(2)}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.green,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.blue.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        menuItem.category,
                        style: TextStyle(
                          color: Colors.blue[700],
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  UserModel? _findRestaurantForMenuItem(String menuItemId) {
    // This is a simplified approach - in a real app, menu items would have restaurantId
    // For now, we'll use the mapping logic from RestaurantController
    String restaurantId = '';
    if (['menu_001', 'menu_002', 'menu_003'].contains(menuItemId)) {
      restaurantId = 'rest_001'; // Tony's Pizzeria
    } else if (['menu_004', 'menu_005', 'menu_006'].contains(menuItemId)) {
      restaurantId = 'rest_002'; // Burger Palace
    }
    
    return _restaurantController.restaurants.firstWhere(
      (restaurant) => restaurant.id == restaurantId,
      orElse: () => _restaurantController.restaurants.first,
    );
  }
}
