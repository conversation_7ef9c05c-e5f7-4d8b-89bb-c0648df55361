import 'dart:convert';
import 'package:flutter/services.dart';
import '../models/cart_item.dart';
import '../models/user.dart';
import '../models/menu_item.dart';
import '../models/order.dart';

class DemoDataService {
  static DemoDataService? _instance;
  static DemoDataService get instance => _instance ??= DemoDataService._();

  DemoDataService._();

  Map<String, dynamic>? _demoData;
  bool _isLoaded = false;

  // Load demo data from JSON file
  Future<void> loadDemoData() async {
    if (_isLoaded) return;

    try {
      final String jsonString = await rootBundle.loadString('demo_data.json');
      _demoData = json.decode(jsonString);
      _isLoaded = true;
    } catch (e) {
      throw Exception('Failed to load demo data: $e');
    }
  }

  // Ensure data is loaded before accessing
  void _ensureDataLoaded() {
    if (!_isLoaded || _demoData == null) {
      throw Exception('Demo data not loaded. Call loadDemoData() first.');
    }
  }

  // Get all users from demo data
  List<UserModel> getUsers() {
    _ensureDataLoaded();
    final List<dynamic> usersJson = _demoData!['users'] ?? [];
    return usersJson.map((json) => UserModel.fromJson(json)).toList();
  }

  // Get users by role
  List<UserModel> getUsersByRole(UserRole role) {
    return getUsers().where((user) => user.role == role).toList();
  }

  // Get specific user by ID
  UserModel? getUserById(String id) {
    try {
      return getUsers().firstWhere((user) => user.id == id);
    } catch (e) {
      return null;
    }
  }

  // Get all menu items from demo data
  List<MenuItem> getMenuItems() {
    _ensureDataLoaded();
    final List<dynamic> menuItemsJson = _demoData!['menuItems'] ?? [];
    return menuItemsJson.map((json) => MenuItem.fromJson(json)).toList();
  }

  // Get menu items for a specific restaurant
  List<MenuItem> getMenuItemsForRestaurant(String restaurantId) {
    final allMenuItems = getMenuItems();

    // Map menu items to restaurants based on the pattern from the original code
    final restaurantMenuMap = {
      'rest_001': ['menu_001', 'menu_002', 'menu_003', 'menu_007'], // Tony's Pizzeria
      'rest_002': ['menu_004', 'menu_005', 'menu_006'], // Burger Palace
    };

    final menuItemIds = restaurantMenuMap[restaurantId] ?? [];
    return allMenuItems.where((item) => menuItemIds.contains(item.id)).toList();
  }

  // Get all payment methods from demo data
  List<PaymentMethod> getPaymentMethods() {
    _ensureDataLoaded();
    final List<dynamic> paymentMethodsJson = _demoData!['paymentMethods'] ?? [];
    return paymentMethodsJson.map((json) => PaymentMethod.fromJson(json)).toList();
  }

  // Get all cart items from demo data
  List<CartItem> getCartItems() {
    _ensureDataLoaded();
    final List<dynamic> cartItemsJson = _demoData!['cartItems'] ?? [];
    return cartItemsJson.map((json) => CartItem.fromJson(json)).toList();
  }

  // Get cart items for a specific restaurant
  List<CartItem> getCartItemsForRestaurant(String restaurantId) {
    return getCartItems().where((item) => item.restaurantId == restaurantId).toList();
  }

  // Get all orders from demo data
  List<OrderModel> getOrders() {
    _ensureDataLoaded();
    final List<dynamic> ordersJson = _demoData!['orders'] ?? [];
    return ordersJson.map((json) => OrderModel.fromJson(json)).toList();
  }

  // Get orders by status
  List<OrderModel> getOrdersByStatus(OrderStatus status) {
    return getOrders().where((order) => order.status == status).toList();
  }

  // Get orders for a specific user
  List<OrderModel> getOrdersForUser(String userId) {
    // In a real app, orders would have a userId field
    // For demo purposes, we'll return all orders
    return getOrders();
  }

  // Get orders for a specific restaurant
  List<OrderModel> getOrdersForRestaurant(String restaurantId) {
    return getOrders().where((order) => order.restaurantId == restaurantId).toList();
  }

  // Get orders for a specific driver
  List<OrderModel> getOrdersForDriver(String driverId) {
    return getOrders().where((order) => order.driverId == driverId).toList();
  }

  // Get restaurant statistics
  Map<String, dynamic> getRestaurantStats(String restaurantId) {
    final menuItems = getMenuItemsForRestaurant(restaurantId);
    final orders = getOrdersForRestaurant(restaurantId);

    final availableItems = menuItems.where((item) => item.isAvailable).length;
    final totalItems = menuItems.length;
    final categories = menuItems.map((item) => item.category).toSet().length;

    final totalRevenue = orders
        .where((order) => order.status == OrderStatus.delivered)
        .fold(0.0, (sum, order) => sum + _calculateOrderTotal(order));

    return {
      'totalMenuItems': totalItems,
      'availableItems': availableItems,
      'unavailableItems': totalItems - availableItems,
      'categories': categories,
      'averagePrice':
          menuItems.isEmpty ? 0.0 : menuItems.map((item) => item.price).reduce((a, b) => a + b) / totalItems,
      'totalOrders': orders.length,
      'totalRevenue': totalRevenue,
    };
  }

  // Get admin statistics
  Map<String, dynamic> getAdminStats() {
    final users = getUsers();
    final orders = getOrders();
    final restaurants = getUsersByRole(UserRole.restaurant);

    final totalRevenue = orders
        .where((order) => order.status == OrderStatus.delivered)
        .fold(0.0, (sum, order) => sum + _calculateOrderTotal(order));

    final todayOrders = orders.where((order) {
      final today = DateTime.now();
      final orderDate = order.placedAt;
      return orderDate.year == today.year && orderDate.month == today.month && orderDate.day == today.day;
    }).length;

    return {
      'totalUsers': users.length,
      'totalCustomers': getUsersByRole(UserRole.customer).length,
      'totalRestaurants': restaurants.length,
      'totalDrivers': getUsersByRole(UserRole.driver).length,
      'totalOrders': orders.length,
      'todayOrders': todayOrders,
      'totalRevenue': totalRevenue,
      'activeRestaurants': restaurants.where((r) => r.isActive).length,
    };
  }

  // Get driver statistics
  Map<String, dynamic> getDriverStats(String driverId) {
    final driverOrders = getOrdersForDriver(driverId);
    final completedOrders = driverOrders.where((order) => order.status == OrderStatus.delivered).toList();

    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final startOfWeek = today.subtract(Duration(days: today.weekday - 1));
    final startOfMonth = DateTime(today.year, today.month, 1);

    final todayOrders = completedOrders.where((order) => order.deliveredAt?.isAfter(startOfDay) ?? false).toList();
    final weekOrders = completedOrders.where((order) => order.deliveredAt?.isAfter(startOfWeek) ?? false).toList();
    final monthOrders = completedOrders.where((order) => order.deliveredAt?.isAfter(startOfMonth) ?? false).toList();

    final todayEarnings = todayOrders.fold(0.0, (sum, order) => sum + order.deliveryFee);
    final weekEarnings = weekOrders.fold(0.0, (sum, order) => sum + order.deliveryFee);
    final monthEarnings = monthOrders.fold(0.0, (sum, order) => sum + order.deliveryFee);

    return {
      'todayDeliveries': todayOrders.length,
      'weekDeliveries': weekOrders.length,
      'monthDeliveries': monthOrders.length,
      'totalDeliveries': completedOrders.length,
      'todayEarnings': todayEarnings,
      'weekEarnings': weekEarnings,
      'monthEarnings': monthEarnings,
      'averageDeliveryTime': _calculateAverageDeliveryTime(completedOrders),
      'rating': 4.8, // Demo rating
    };
  }

  // Helper method to calculate order total
  double _calculateOrderTotal(OrderModel order) {
    final itemsTotal = order.items.fold(0.0, (sum, item) => sum + (item.menuItem.price * item.quantity));
    return itemsTotal + order.deliveryFee;
  }

  // Helper method to calculate average delivery time
  double _calculateAverageDeliveryTime(List<OrderModel> orders) {
    if (orders.isEmpty) return 0.0;

    final totalMinutes = orders.fold(0.0, (sum, order) {
      if (order.deliveredAt != null) {
        final deliveryTime = order.deliveredAt!.difference(order.placedAt).inMinutes;
        return sum + deliveryTime;
      }
      return sum;
    });

    return totalMinutes / orders.length;
  }

  // Get menu categories
  List<String> getMenuCategories() {
    final menuItems = getMenuItems();
    final categories = menuItems.map((item) => item.category).toSet().toList();
    categories.sort();
    return ['All', ...categories];
  }

  // Search functionality
  List<UserModel> searchUsers(String query) {
    if (query.isEmpty) return getUsers();

    final lowercaseQuery = query.toLowerCase();
    return getUsers()
        .where((user) =>
            user.firstName.toLowerCase().contains(lowercaseQuery) ||
            user.lastName.toLowerCase().contains(lowercaseQuery) ||
            user.email.toLowerCase().contains(lowercaseQuery) ||
            (user.phone?.contains(query) ?? false))
        .toList();
  }

  List<MenuItem> searchMenuItems(String query) {
    if (query.isEmpty) return getMenuItems();

    final lowercaseQuery = query.toLowerCase();
    return getMenuItems()
        .where((item) =>
            item.name.toLowerCase().contains(lowercaseQuery) ||
            item.description.toLowerCase().contains(lowercaseQuery) ||
            item.category.toLowerCase().contains(lowercaseQuery))
        .toList();
  }

  List<OrderModel> searchOrders(String query) {
    if (query.isEmpty) return getOrders();

    return getOrders().where((order) => order.id.contains(query) || order.restaurantId.contains(query)).toList();
  }
}
