import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/cart_controller.dart';
import '../../core/app_routes.dart';
import '../../models/cart_item.dart';
import '../shared/custom_app_bar.dart';
import '../shared/custom_cards.dart';
import '../shared/custom_buttons.dart';
import '../shared/loading_widget.dart';
import '../shared/empty_state_widget.dart';

class CartScreen extends StatefulWidget {
  const CartScreen({super.key});

  @override
  State<CartScreen> createState() => _CartScreenState();
}

class _CartScreenState extends State<CartScreen> {
  final CartController _cartController = Get.find<CartController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: const CustomAppBar(
        title: 'My Cart',
      ),
      body: Obx(() {
        if (_cartController.isLoading) {
          return const LoadingWidget(message: 'Loading cart...');
        }

        final cartItems = _cartController.cartItems;
        if (cartItems.isEmpty) {
          return const EmptyStateWidget(
            title: 'Your cart is empty',
            message: 'Add some delicious items to get started!',
            icon: Icons.shopping_cart_outlined,
            actionText: 'Browse Restaurants',
          );
        }

        return Column(
          children: [
            // Cart Items
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: cartItems.length,
                itemBuilder: (context, index) {
                  final cartItem = cartItems[index];
                  return _buildCartItemCard(cartItem);
                },
              ),
            ),
            // Order Summary
            _buildOrderSummary(),
          ],
        );
      }),
    );
  }

  Widget _buildCartItemCard(CartItem cartItem) {
    return CustomCard(
      margin: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          // Item Image
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.fastfood,
              size: 30,
              color: Colors.grey,
            ),
          ),
          const SizedBox(width: 16),
          // Item Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  cartItem.menuItem.name,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '\$${cartItem.menuItem.price.toStringAsFixed(2)}',
                  style: TextStyle(
                    color: Theme.of(context).primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (cartItem.specialInstructions.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    'Note: ${cartItem.specialInstructions}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ],
            ),
          ),
          // Quantity Controls
          Column(
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    onPressed: () => _decreaseQuantity(cartItem),
                    icon: const Icon(Icons.remove_circle_outline),
                    iconSize: 20,
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                  ),
                  Container(
                    width: 40,
                    height: 32,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey[300]!),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Center(
                      child: Text(
                        '${cartItem.quantity}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => _increaseQuantity(cartItem),
                    icon: const Icon(Icons.add_circle_outline),
                    iconSize: 20,
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                '\$${cartItem.totalPrice.toStringAsFixed(2)}',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          // Remove Button
          IconButton(
            onPressed: () => _removeItem(cartItem),
            icon: const Icon(Icons.delete_outline),
            color: Colors.red,
          ),
        ],
      ),
    );
  }

  Widget _buildOrderSummary() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 8,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'Order Summary',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildSummaryRow(
            'Subtotal',
            '\$${_cartController.subtotal.toStringAsFixed(2)}',
          ),
          _buildSummaryRow(
            'Delivery Fee',
            '\$${_cartController.deliveryFee.toStringAsFixed(2)}',
          ),
          _buildSummaryRow(
            'Tax',
            '\$${_cartController.tax.toStringAsFixed(2)}',
          ),
          const Divider(height: 24),
          _buildSummaryRow(
            'Total',
            '\$${_cartController.total.toStringAsFixed(2)}',
            isTotal: true,
          ),
          const SizedBox(height: 24),
          PrimaryButton(
            text: 'Proceed to Checkout',
            onPressed: _proceedToCheckout,
            width: double.infinity,
          ),
          const SizedBox(height: 8),
          SecondaryButton(
            text: 'Continue Shopping',
            onPressed: () => Get.back(),
            width: double.infinity,
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 18 : 16,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: isTotal ? 18 : 16,
              fontWeight: FontWeight.bold,
              color: isTotal ? Theme.of(context).primaryColor : null,
            ),
          ),
        ],
      ),
    );
  }

  void _increaseQuantity(CartItem cartItem) {
    _cartController.updateQuantity(cartItem.id, cartItem.quantity + 1);
  }

  void _decreaseQuantity(CartItem cartItem) {
    if (cartItem.quantity > 1) {
      _cartController.updateQuantity(cartItem.id, cartItem.quantity - 1);
    } else {
      _removeItem(cartItem);
    }
  }

  void _removeItem(CartItem cartItem) {
    _cartController.removeItem(cartItem.id);
  }

  void _proceedToCheckout() {
    if (_cartController.cartItems.isEmpty) {
      ErrorSnackBar.show(context, 'Your cart is empty');
      return;
    }
    Get.toNamed(AppRoutes.checkout);
  }
}
