import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../shared/custom_app_bar.dart';
import '../shared/custom_cards.dart';
import '../shared/custom_buttons.dart';
import '../shared/error_widget.dart';
import '../shared/empty_state_widget.dart';

class UserManagementScreen extends StatefulWidget {
  const UserManagementScreen({super.key});

  @override
  State<UserManagementScreen> createState() => _UserManagementScreenState();
}

class _UserManagementScreenState extends State<UserManagementScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final List<String> _tabs = ['All Users', 'Customers', 'Restaurants', 'Drivers'];

  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
    _loadUsers();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadUsers() async {
    // TODO: Implement load all users
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: CustomAppBar(
        title: 'User Management',
        bottom: TabBar(
          controller: _tabController,
          tabs: _tabs.map((tab) => Tab(text: tab)).toList(),
          labelColor: Theme.of(context).primaryColor,
          unselectedLabelColor: Colors.grey,
          indicatorColor: Theme.of(context).primaryColor,
          isScrollable: true,
        ),
      ),
      body: Column(
        children: [
          // Search Bar
          _buildSearchBar(),
          // User List
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: _tabs.map((tab) => _buildUserList(tab)).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search users...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    setState(() {
                      _searchQuery = '';
                    });
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey[300]!),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey[300]!),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Theme.of(context).primaryColor),
          ),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
      ),
    );
  }

  Widget _buildUserList(String userType) {
    // Mock users data - in real app this would come from controller
    final allUsers = _getMockUsers();
    final filteredUsers = _filterUsers(allUsers, userType);

    if (filteredUsers.isEmpty) {
      return EmptyStateWidget(
        title: 'No Users Found',
        message: 'No users match your search criteria.',
        icon: Icons.people,
      );
    }

    return RefreshIndicator(
      onRefresh: _loadUsers,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: filteredUsers.length,
        itemBuilder: (context, index) {
          final user = filteredUsers[index];
          return _buildUserCard(user);
        },
      ),
    );
  }

  Widget _buildUserCard(Map<String, dynamic> user) {
    return CustomCard(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        children: [
          Row(
            children: [
              // User Avatar
              CircleAvatar(
                radius: 25,
                backgroundColor: Theme.of(context).primaryColor.withOpacity(0.1),
                child: user['avatar'] != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(25),
                        child: Image.network(
                          user['avatar'],
                          width: 50,
                          height: 50,
                          fit: BoxFit.cover,
                        ),
                      )
                    : Icon(
                        Icons.person,
                        color: Theme.of(context).primaryColor,
                      ),
              ),
              const SizedBox(width: 16),
              // User Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            user['name'],
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: _getUserTypeColor(user['type']).withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            user['type'].toString().toUpperCase(),
                            style: TextStyle(
                              color: _getUserTypeColor(user['type']),
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      user['email'],
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.phone,
                          size: 14,
                          color: Colors.grey[500],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          user['phone'] ?? 'No phone',
                          style: TextStyle(
                            color: Colors.grey[500],
                            fontSize: 12,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Icon(
                          user['isActive'] ? Icons.check_circle : Icons.cancel,
                          size: 14,
                          color: user['isActive'] ? Colors.green : Colors.red,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          user['isActive'] ? 'Active' : 'Inactive',
                          style: TextStyle(
                            color: user['isActive'] ? Colors.green : Colors.red,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // User Stats
          if (user['stats'] != null) ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem('Orders', '${user['stats']['orders']}'),
                  _buildStatItem('Rating', '${user['stats']['rating']}'),
                  if (user['type'] == 'restaurant') _buildStatItem('Revenue', '\$${user['stats']['revenue']}'),
                  if (user['type'] == 'driver') _buildStatItem('Deliveries', '${user['stats']['deliveries']}'),
                ],
              ),
            ),
            const SizedBox(height: 12),
          ],
          // Action Buttons
          Row(
            children: [
              Expanded(
                child: SecondaryButton(
                  text: 'View Details',
                  onPressed: () => _viewUserDetails(user),
                  height: 36,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: SecondaryButton(
                  text: user['isActive'] ? 'Suspend' : 'Activate',
                  onPressed: () => _toggleUserStatus(user),
                  textColor: user['isActive'] ? Colors.red : Colors.green,
                  borderColor: user['isActive'] ? Colors.red : Colors.green,
                  height: 36,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Color _getUserTypeColor(String type) {
    switch (type) {
      case 'customer':
        return Colors.blue;
      case 'restaurant':
        return Colors.green;
      case 'driver':
        return Colors.orange;
      case 'admin':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  List<Map<String, dynamic>> _getMockUsers() {
    return [
      {
        'id': '1',
        'name': 'John Doe',
        'email': '<EMAIL>',
        'phone': '+1234567890',
        'type': 'customer',
        'isActive': true,
        'stats': {'orders': 25, 'rating': 4.8},
      },
      {
        'id': '2',
        'name': 'Pizza Palace',
        'email': '<EMAIL>',
        'phone': '+1234567891',
        'type': 'restaurant',
        'isActive': true,
        'stats': {'orders': 150, 'rating': 4.5, 'revenue': 12500},
      },
      {
        'id': '3',
        'name': 'Mike Driver',
        'email': '<EMAIL>',
        'phone': '+1234567892',
        'type': 'driver',
        'isActive': true,
        'stats': {'orders': 89, 'rating': 4.9, 'deliveries': 89},
      },
      {
        'id': '4',
        'name': 'Jane Smith',
        'email': '<EMAIL>',
        'phone': '+1234567893',
        'type': 'customer',
        'isActive': false,
        'stats': {'orders': 5, 'rating': 4.2},
      },
    ];
  }

  List<Map<String, dynamic>> _filterUsers(List<Map<String, dynamic>> users, String userType) {
    var filtered = users;

    // Filter by user type
    if (userType != 'All Users') {
      filtered = filtered.where((user) {
        return user['type'] == userType.toLowerCase().replaceAll('s', '');
      }).toList();
    }

    // Filter by search query
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((user) {
        return user['name'].toLowerCase().contains(_searchQuery.toLowerCase()) ||
            user['email'].toLowerCase().contains(_searchQuery.toLowerCase());
      }).toList();
    }

    return filtered;
  }

  void _viewUserDetails(Map<String, dynamic> user) {
    Get.dialog(
      AlertDialog(
        title: Text('User Details: ${user['name']}'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Email: ${user['email']}'),
              Text('Phone: ${user['phone'] ?? 'Not provided'}'),
              Text('Type: ${user['type']}'),
              Text('Status: ${user['isActive'] ? 'Active' : 'Inactive'}'),
              if (user['stats'] != null) ...[
                const SizedBox(height: 8),
                const Text('Statistics:', style: TextStyle(fontWeight: FontWeight.bold)),
                ...user['stats'].entries.map((entry) => Text('${entry.key}: ${entry.value}')),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _toggleUserStatus(Map<String, dynamic> user) {
    final action = user['isActive'] ? 'suspend' : 'activate';
    Get.dialog(
      AlertDialog(
        title: Text('${action.capitalizeFirst ?? action} User'),
        content: Text('Are you sure you want to $action ${user['name']}?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              setState(() {
                user['isActive'] = !user['isActive'];
              });
              SuccessSnackBar.show(
                context,
                '${user['name']} has been ${user['isActive'] ? 'activated' : 'suspended'}',
              );
            },
            child: Text(
              action.capitalizeFirst ?? action,
              style: TextStyle(
                color: user['isActive'] ? Colors.red : Colors.green,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
