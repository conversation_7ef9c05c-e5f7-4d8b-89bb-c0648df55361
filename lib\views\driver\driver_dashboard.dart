import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/user_controller.dart';
import '../../controllers/order_controller.dart';
import '../../controllers/driver_controller.dart';
import '../../models/order.dart';
import '../../core/app_routes.dart';
import '../shared/custom_app_bar.dart';
import '../shared/custom_cards.dart';
import '../shared/custom_buttons.dart';
import '../shared/loading_widget.dart';
import '../shared/error_widget.dart';

class DriverDashboard extends StatefulWidget {
  const DriverDashboard({super.key});

  @override
  State<DriverDashboard> createState() => _DriverDashboardState();
}

class _DriverDashboardState extends State<DriverDashboard> {
  final UserController _userController = Get.find<UserController>();
  final OrderController _orderController = Get.find<OrderController>();
  final DriverController _driverController = Get.find<DriverController>();

  bool _isOnline = false;
  final bool _hasActiveDelivery = false;

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    final driver = _userController.currentUser;
    if (driver != null) {
      await _orderController.loadAvailableOrders();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: ProfileAppBar(
        title: 'Driver Dashboard',
        userName: _userController.currentUser?.fullName ?? 'Driver',
        userImageUrl: _userController.currentUser?.profileImageUrl,
        onProfileTap: () => Get.toNamed(AppRoutes.profile),
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {
              InfoSnackBar.show(context, 'Notifications coming soon!');
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadDashboardData,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Driver Status
              _buildDriverStatus(),
              // Today's Stats
              _buildTodayStats(),
              // Current Delivery
              _buildCurrentDelivery(),
              // Available Orders
              _buildAvailableOrders(),
              // Quick Actions
              _buildQuickActions(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDriverStatus() {
    final driver = _userController.currentUser;
    if (driver == null) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors:
              _isOnline ? [Colors.green, Colors.green.withOpacity(0.8)] : [Colors.grey, Colors.grey.withOpacity(0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Welcome back, ${driver.fullName.split(' ').first}!',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _isOnline ? 'You are online and ready for deliveries' : 'You are offline',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.white70,
                      ),
                    ),
                  ],
                ),
              ),
              Switch(
                value: _isOnline,
                onChanged: (value) {
                  setState(() {
                    _isOnline = value;
                  });
                  SuccessSnackBar.show(
                    context,
                    _isOnline ? 'You are now online!' : 'You are now offline',
                  );
                },
                activeColor: Colors.white,
                activeTrackColor: Colors.white.withOpacity(0.3),
              ),
            ],
          ),
          if (_isOnline) ...[
            const SizedBox(height: 16),
            Row(
              children: [
                const Icon(Icons.location_on, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    driver.stats.address.fullAddress,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTodayStats() {
    final driver = _userController.currentUser;
    if (driver == null) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Today\'s Performance',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: StatCard(
                  title: 'Deliveries',
                  value: '${driver.stats.totalOrders}',
                  icon: Icons.delivery_dining,
                  color: Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: StatCard(
                  title: 'Earnings',
                  value: '\$${driver.stats.totalRevenue.toStringAsFixed(0)}',
                  icon: Icons.attach_money,
                  color: Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: StatCard(
                  title: 'Rating',
                  value: driver.stats.rate.rating.toStringAsFixed(1),
                  icon: Icons.star,
                  color: Colors.orange,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: StatCard(
                  title: 'Distance',
                  value: '${(driver.stats.totalRevenue / 10).toStringAsFixed(1)} km',
                  icon: Icons.route,
                  color: Colors.purple,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentDelivery() {
    // Mock current delivery - in real app this would come from controller
    final hasCurrentDelivery = _hasActiveDelivery;

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Current Delivery',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 12),
          if (hasCurrentDelivery)
            _buildCurrentDeliveryCard()
          else
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.delivery_dining,
                    size: 48,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'No active delivery',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _isOnline ? 'Accept an order to start delivering' : 'Go online to see available orders',
                    style: TextStyle(
                      color: Colors.grey[500],
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildCurrentDeliveryCard() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Order #12345',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.blue[100],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'IN TRANSIT',
                  style: TextStyle(
                    color: Colors.blue[800],
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          const Text('McDonald\'s → John Doe'),
          const SizedBox(height: 8),
          const Text('123 Main St, City'),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: SecondaryButton(
                  text: 'Call Customer',
                  onPressed: () {},
                  height: 36,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: PrimaryButton(
                  text: 'Navigate',
                  onPressed: () {},
                  height: 36,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAvailableOrders() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Available Orders',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              TextButton(
                onPressed: () {
                  Get.toNamed(AppRoutes.availableDeliveries);
                },
                child: const Text('View All'),
              ),
            ],
          ),
          const SizedBox(height: 12),
          if (!_isOnline)
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.orange[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.orange[200]!),
              ),
              child: Row(
                children: [
                  Icon(Icons.info, color: Colors.orange[700]),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Go online to see available orders',
                      style: TextStyle(color: Colors.orange[700]),
                    ),
                  ),
                ],
              ),
            )
          else
            Obx(() {
              if (_orderController.isLoading) {
                return const LoadingWidget(message: 'Loading orders...');
              }

              if (_orderController.errorMessage.isNotEmpty) {
                return CustomErrorWidget(
                  message: _orderController.errorMessage,
                  onRetry: _loadDashboardData,
                );
              }

              // Get real available orders from driver controller
              final availableOrders = _driverController.availableOrders;

              if (availableOrders.isEmpty) {
                return Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Center(
                    child: Text('No available orders at the moment'),
                  ),
                );
              }

              return Column(
                children: availableOrders.map((order) => _buildAvailableOrderCard(order)).toList(),
              );
            }),
        ],
      ),
    );
  }

  Widget _buildAvailableOrderCard(OrderModel order) {
    return CustomCard(
      margin: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                order.restaurantName,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              Text(
                '\$${order.deliveryFee.toStringAsFixed(2)}',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              const Icon(Icons.person, size: 16, color: Colors.grey),
              const SizedBox(width: 4),
              Text(order.customerName),
              const SizedBox(width: 16),
              const Icon(Icons.shopping_bag, size: 16, color: Colors.grey),
              const SizedBox(width: 4),
              Text('${order.items.length} items'),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              const Icon(Icons.location_on, size: 16, color: Colors.grey),
              const SizedBox(width: 4),
              Text(order.deliveryAddress),
              const Spacer(),
              PrimaryButton(
                text: 'Accept',
                onPressed: () => _acceptOrder(order),
                height: 32,
                width: 80,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quick Actions',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: ActionCard(
                  title: 'Earnings',
                  subtitle: 'View earnings history',
                  icon: Icons.account_balance_wallet,
                  onTap: () {
                    InfoSnackBar.show(context, 'Earnings history coming soon!');
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ActionCard(
                  title: 'Support',
                  subtitle: 'Get help',
                  icon: Icons.support_agent,
                  onTap: () {
                    InfoSnackBar.show(context, 'Support coming soon!');
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _acceptOrder(OrderModel order) async {
    try {
      // Show loading state
      if (mounted) {
        InfoSnackBar.show(context, 'Accepting order...');
      }

      // Accept order using driver controller
      final success = await _driverController.acceptOrder(order.id);

      if (success) {
        if (mounted) {
          SuccessSnackBar.show(
            context,
            'Order from ${order.restaurantName} accepted!',
          );
        }

        // Refresh dashboard data
        await _loadDashboardData();
      } else {
        if (mounted) {
          ErrorSnackBar.show(
            context,
            _driverController.errorMessage.isNotEmpty ? _driverController.errorMessage : 'Failed to accept order',
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ErrorSnackBar.show(
          context,
          'Error accepting order: ${e.toString()}',
        );
      }
    }
  }
}
