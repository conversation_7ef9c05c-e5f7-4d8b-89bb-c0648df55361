# Views Directory Structure

This directory contains all the UI views for the Restaurant Hub application.

## Structure:
- `shared/` - Common/reusable UI components
- `auth/` - Authentication related views
- `customer/` - Customer-specific views
- `restaurant/` - Restaurant owner views
- `driver/` - Driver-specific views
- `admin/` - Admin panel views
- `navigation/` - Navigation components

## Usage:
Each subdirectory contains views specific to that user role or functionality.
The shared directory contains reusable components used across multiple views.
