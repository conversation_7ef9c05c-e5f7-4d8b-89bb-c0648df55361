import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/user_controller.dart';
import '../../models/user.dart';
import '../shared/custom_app_bar.dart';
import '../shared/custom_cards.dart';
import '../shared/custom_buttons.dart';
import '../shared/loading_widget.dart';

class RestaurantProfileScreen extends StatefulWidget {
  const RestaurantProfileScreen({super.key});

  @override
  State<RestaurantProfileScreen> createState() => _RestaurantProfileScreenState();
}

class _RestaurantProfileScreenState extends State<RestaurantProfileScreen> {
  final UserController _userController = Get.find<UserController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: const CustomAppBar(
        title: 'Restaurant Profile',
      ),
      body: Obx(() {
        final restaurant = _userController.currentUser;
        if (restaurant == null) {
          return const LoadingWidget(message: 'Loading profile...');
        }

        return SingleChildScrollView(
          child: Column(
            children: [
              // Restaurant Header
              _buildRestaurantHeader(restaurant),
              const SizedBox(height: 16),
              // Restaurant Stats
              _buildRestaurantStats(restaurant),
              const SizedBox(height: 16),
              // Business Information
              _buildBusinessInformation(restaurant),
              const SizedBox(height: 16),
              // Operating Hours
              _buildOperatingHours(),
              const SizedBox(height: 16),
              // Settings
              _buildSettings(),
              const SizedBox(height: 32),
              // Logout Button
              _buildLogoutButton(),
              const SizedBox(height: 32),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildRestaurantHeader(UserModel restaurant) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Restaurant Logo
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(16),
            ),
            child: restaurant.profileImageUrl != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: Image.network(
                      restaurant.profileImageUrl!,
                      fit: BoxFit.cover,
                    ),
                  )
                : Icon(
                    Icons.restaurant,
                    size: 50,
                    color: Theme.of(context).primaryColor,
                  ),
          ),
          const SizedBox(height: 16),
          // Restaurant Name
          Text(
            restaurant.fullName,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          // Restaurant Email
          Text(
            restaurant.email,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          // Restaurant Address
          Text(
            restaurant.stats.address.fullAddress,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          // Status Badge
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.green,
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Text(
              'OPEN',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRestaurantStats(UserModel restaurant) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Restaurant Statistics',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'Total Orders',
                  '${restaurant.stats.totalOrders}',
                  Icons.receipt_long,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  'Rating',
                  restaurant.stats.rate.rating.toStringAsFixed(1),
                  Icons.star,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'Revenue',
                  '\$${restaurant.stats.totalRevenue.toStringAsFixed(0)}',
                  Icons.attach_money,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  'Menu Items',
                  '${restaurant.stats.totalMenuItems}',
                  Icons.restaurant_menu,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: Theme.of(context).primaryColor,
          size: 24,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildBusinessInformation(UserModel restaurant) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          _buildInfoTile(
            icon: Icons.business,
            title: 'Business Information',
            subtitle: 'Update restaurant details',
            onTap: () {
              InfoSnackBar.show(context, 'Business info editing coming soon!');
            },
          ),
          _buildDivider(),
          _buildInfoTile(
            icon: Icons.location_on,
            title: 'Address & Delivery',
            subtitle: 'Manage delivery zones',
            onTap: () {
              InfoSnackBar.show(context, 'Address management coming soon!');
            },
          ),
          _buildDivider(),
          _buildInfoTile(
            icon: Icons.payment,
            title: 'Payment Settings',
            subtitle: 'Bank account & payment info',
            onTap: () {
              InfoSnackBar.show(context, 'Payment settings coming soon!');
            },
          ),
        ],
      ),
    );
  }

  Widget _buildOperatingHours() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          _buildInfoTile(
            icon: Icons.access_time,
            title: 'Operating Hours',
            subtitle: 'Set your business hours',
            onTap: () {
              _showOperatingHoursDialog();
            },
          ),
          _buildDivider(),
          _buildInfoTile(
            icon: Icons.pause_circle,
            title: 'Temporary Closure',
            subtitle: 'Close restaurant temporarily',
            onTap: () {
              _showTemporaryClosureDialog();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSettings() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          _buildInfoTile(
            icon: Icons.notifications,
            title: 'Notifications',
            subtitle: 'Manage notification preferences',
            onTap: () {
              InfoSnackBar.show(context, 'Notification settings coming soon!');
            },
          ),
          _buildDivider(),
          _buildInfoTile(
            icon: Icons.security,
            title: 'Security',
            subtitle: 'Password and security settings',
            onTap: () {
              InfoSnackBar.show(context, 'Security settings coming soon!');
            },
          ),
          _buildDivider(),
          _buildInfoTile(
            icon: Icons.help,
            title: 'Help & Support',
            subtitle: 'Get help and contact support',
            onTap: () {
              InfoSnackBar.show(context, 'Help & support coming soon!');
            },
          ),
        ],
      ),
    );
  }

  Widget _buildInfoTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: Theme.of(context).primaryColor,
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(subtitle),
      trailing: const Icon(
        Icons.chevron_right,
        color: Colors.grey,
      ),
      onTap: onTap,
    );
  }

  Widget _buildDivider() {
    return Divider(
      height: 1,
      indent: 56,
      color: Colors.grey[200],
    );
  }

  Widget _buildLogoutButton() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: SecondaryButton(
        text: 'Logout',
        onPressed: _handleLogout,
        width: double.infinity,
        textColor: Colors.red,
        borderColor: Colors.red,
      ),
    );
  }

  void _showOperatingHoursDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('Operating Hours'),
        content: const Text('Operating hours management coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showTemporaryClosureDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('Temporary Closure'),
        content: const Text('Are you sure you want to temporarily close your restaurant?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              SuccessSnackBar.show(context, 'Restaurant temporarily closed');
            },
            child: const Text(
              'Close',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  void _handleLogout() {
    Get.dialog(
      AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              _userController.logout();
            },
            child: const Text(
              'Logout',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}
