import 'cart_item.dart';

enum OrderStatus {
  pending,
  placed,
  confirmed,
  preparing,
  ready,
  pickedUp,
  onTheWay,
  delivered,
  cancelled,
}

extension OrderStatusExtension on OrderStatus {
  String get displayName {
    switch (this) {
      case OrderStatus.pending:
        return 'Pending';
      case OrderStatus.placed:
        return 'Order Placed';
      case OrderStatus.confirmed:
        return 'Confirmed';
      case OrderStatus.preparing:
        return 'Preparing';
      case OrderStatus.ready:
        return 'Ready for Pickup';
      case OrderStatus.pickedUp:
        return 'Picked Up';
      case OrderStatus.onTheWay:
        return 'On the Way';
      case OrderStatus.delivered:
        return 'Delivered';
      case OrderStatus.cancelled:
        return 'Cancelled';
    }
  }

  String get description {
    switch (this) {
      case OrderStatus.pending:
        return 'Your order is being processed';
      case OrderStatus.placed:
        return 'Your order has been placed successfully';
      case OrderStatus.confirmed:
        return 'Restaurant has confirmed your order';
      case OrderStatus.preparing:
        return 'Your food is being prepared';
      case OrderStatus.ready:
        return 'Your order is ready for pickup';
      case OrderStatus.pickedUp:
        return 'Driver has picked up your order';
      case OrderStatus.onTheWay:
        return 'Your order is on the way';
      case OrderStatus.delivered:
        return 'Order delivered successfully';
      case OrderStatus.cancelled:
        return 'Order has been cancelled';
    }
  }

  bool get isActive => this != OrderStatus.delivered && this != OrderStatus.cancelled;
}

class OrderModel {
  final String id;
  final List<CartItem> items;
  final String restaurantId;
  final double deliveryFee;
  final OrderStatus status;
  final PaymentMethod paymentMethod;
  final DateTime placedAt;
  final DateTime? estimatedDeliveryTime;
  final DateTime? deliveredAt;
  final String? driverId;
  final String? specialInstructions;

  OrderModel({
    required this.id,
    required this.items,
    required this.restaurantId,
    required this.deliveryFee,
    required this.status,
    required this.paymentMethod,
    required this.placedAt,
    this.estimatedDeliveryTime,
    this.deliveredAt,
    this.driverId,
    this.specialInstructions,
  });

  int get totalItems => items.fold(0, (sum, item) => sum + item.quantity);

  OrderModel copyWith({
    String? id,
    List<CartItem>? items,
    String? restaurantId,
    double? deliveryFee,
    OrderStatus? status,
    PaymentMethod? paymentMethod,
    DateTime? placedAt,
    DateTime? estimatedDeliveryTime,
    DateTime? deliveredAt,
    String? driverId,
    String? specialInstructions,
  }) =>
      OrderModel(
        id: id ?? this.id,
        items: items ?? this.items,
        restaurantId: restaurantId ?? this.restaurantId,
        deliveryFee: deliveryFee ?? this.deliveryFee,
        status: status ?? this.status,
        paymentMethod: paymentMethod ?? this.paymentMethod,
        placedAt: placedAt ?? this.placedAt,
        estimatedDeliveryTime: estimatedDeliveryTime ?? this.estimatedDeliveryTime,
        deliveredAt: deliveredAt ?? this.deliveredAt,
        driverId: driverId ?? this.driverId,
        specialInstructions: specialInstructions ?? this.specialInstructions,
      );

  factory OrderModel.fromJson(Map<String, dynamic> json) => OrderModel(
        id: json['id'],
        items: json['items'] != null ? (json['items'] as List).map((item) => CartItem.fromJson(item)).toList() : [],
        restaurantId: json['restaurantId'],
        deliveryFee: json['deliveryFee'].toDouble(),
        status: OrderStatus.values.firstWhere((e) => e.name == json['status']),
        paymentMethod: PaymentMethod.fromJson(json['paymentMethod']),
        placedAt: DateTime.parse(json['placedAt']),
        estimatedDeliveryTime:
            json['estimatedDeliveryTime'] != null ? DateTime.parse(json['estimatedDeliveryTime']) : null,
        deliveredAt: json['deliveredAt'] != null ? DateTime.parse(json['deliveredAt']) : null,
        driverId: json['driverId'],
        specialInstructions: json['specialInstructions'],
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'items': items.map((item) => item.toJson()).toList(),
        'restaurantId': restaurantId,
        'deliveryFee': deliveryFee,
        'status': status.name,
        'paymentMethod': paymentMethod.toJson(),
        'placedAt': placedAt.toIso8601String(),
        'estimatedDeliveryTime': estimatedDeliveryTime?.toIso8601String(),
        'deliveredAt': deliveredAt?.toIso8601String(),
        'driverId': driverId,
        'specialInstructions': specialInstructions,
      };
}

enum PaymentType {
  creditCard,
  debitCard,
  paypal,
  applePay,
  googlePay,
  cash,
}

extension PaymentTypeExtension on PaymentType {
  String get displayName {
    switch (this) {
      case PaymentType.creditCard:
        return 'Credit Card';
      case PaymentType.debitCard:
        return 'Debit Card';
      case PaymentType.paypal:
        return 'PayPal';
      case PaymentType.applePay:
        return 'Apple Pay';
      case PaymentType.googlePay:
        return 'Google Pay';
      case PaymentType.cash:
        return 'Cash on Delivery';
    }
  }

  String get iconPath {
    switch (this) {
      case PaymentType.creditCard:
      case PaymentType.debitCard:
        return 'assets/icons/credit_card.png';
      case PaymentType.paypal:
        return 'assets/icons/paypal.png';
      case PaymentType.applePay:
        return 'assets/icons/apple_pay.png';
      case PaymentType.googlePay:
        return 'assets/icons/google_pay.png';
      case PaymentType.cash:
        return 'assets/icons/cash.png';
    }
  }
}

class PaymentMethod {
  final String id;
  final PaymentType type;
  final String displayName;
  final String? cardNumber; // Last 4 digits for cards
  final String? expiryDate;
  final String? cardHolderName;
  final bool isDefault;

  PaymentMethod({
    required this.id,
    required this.type,
    required this.displayName,
    this.cardNumber,
    this.expiryDate,
    this.cardHolderName,
    this.isDefault = false,
  });

  String get maskedCardNumber {
    if (cardNumber == null) return '';
    return '**** **** **** $cardNumber';
  }

  PaymentMethod copyWith({
    String? id,
    PaymentType? type,
    String? displayName,
    String? cardNumber,
    String? expiryDate,
    String? cardHolderName,
    bool? isDefault,
  }) =>
      PaymentMethod(
        id: id ?? this.id,
        type: type ?? this.type,
        displayName: displayName ?? this.displayName,
        cardNumber: cardNumber ?? this.cardNumber,
        expiryDate: expiryDate ?? this.expiryDate,
        cardHolderName: cardHolderName ?? this.cardHolderName,
        isDefault: isDefault ?? this.isDefault,
      );

  factory PaymentMethod.fromJson(Map<String, dynamic> json) => PaymentMethod(
        id: json['id'],
        type: PaymentType.values.firstWhere((e) => e.name == json['type']),
        displayName: json['displayName'],
        cardNumber: json['cardNumber'],
        expiryDate: json['expiryDate'],
        cardHolderName: json['cardHolderName'],
        isDefault: json['isDefault'] ?? false,
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'type': type.name,
        'displayName': displayName,
        'cardNumber': cardNumber,
        'expiryDate': expiryDate,
        'cardHolderName': cardHolderName,
        'isDefault': isDefault,
      };
}

class PaymentResult {
  final bool success;
  final String? transactionId;
  final String? errorMessage;
  final DateTime? processedAt;

  PaymentResult({
    required this.success,
    this.transactionId,
    this.errorMessage,
    this.processedAt,
  });

  factory PaymentResult.success(String transactionId) => PaymentResult(
        success: true,
        transactionId: transactionId,
        processedAt: DateTime.now(),
      );

  factory PaymentResult.failure(String errorMessage) => PaymentResult(
        success: false,
        errorMessage: errorMessage,
      );
}
