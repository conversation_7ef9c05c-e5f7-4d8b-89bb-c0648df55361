import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../views/auth/splash_screen.dart';
import '../views/auth/login_screen.dart';
import '../views/auth/register_screen.dart';
import '../views/customer/home_screen.dart';
import '../views/customer/restaurant_list_screen.dart';

import '../views/customer/profile_screen.dart';

import '../views/restaurant/restaurant_dashboard.dart';
import '../views/restaurant/menu_management_screen.dart';
import '../views/restaurant/order_management_screen.dart';
import '../views/restaurant/restaurant_profile_screen.dart';
import '../views/driver/driver_dashboard.dart';
import '../views/driver/driver_earnings_screen.dart';
import '../views/driver/driver_profile_screen.dart';

import '../views/admin/admin_dashboard.dart';
import '../views/admin/user_management_screen.dart';
import '../views/admin/analytics_screen.dart';
import '../views/navigation/main_navigation.dart';

class AppRoutes {
  static const String initial = '/';
  static const String home = '/home';
  static const String login = '/login';
  static const String register = '/register';
  static const String profile = '/profile';
  static const String restaurant = '/restaurant';
  static const String restaurants = '/restaurants';
  static const String menu = '/menu';
  static const String cart = '/cart';
  static const String checkout = '/checkout';
  static const String orders = '/orders';
  static const String orderDetails = '/order-details';
  static const String admin = '/admin';
  static const String driver = '/driver';

  // Restaurant routes
  static const String restaurantDashboard = '/restaurant-dashboard';
  static const String menuManagement = '/menu-management';
  static const String orderManagement = '/order-management';
  static const String restaurantProfile = '/restaurant-profile';

  // Driver routes
  static const String driverDashboard = '/driver-dashboard';
  static const String driverEarnings = '/driver-earnings';
  static const String driverProfile = '/driver-profile';

  // Admin routes
  static const String adminDashboard = '/admin-dashboard';
  static const String userManagement = '/user-management';
  static const String analytics = '/analytics';

  // Navigation routes
  static const String mainNavigation = '/main-navigation';

  static List<GetPage> routes = [
    GetPage(
      name: initial,
      page: () => const SplashScreen(),
    ),
    GetPage(
      name: home,
      page: () => const CustomerHomeScreen(),
    ),
    GetPage(
      name: restaurants,
      page: () => const RestaurantListScreen(),
    ),
    GetPage(
      name: login,
      page: () => const LoginScreen(),
    ),
    GetPage(
      name: register,
      page: () => const RegisterScreen(),
    ),
    GetPage(
      name: profile,
      page: () => const CustomerProfileScreen(),
    ),
    GetPage(
      name: restaurant,
      page: () => const RestaurantScreen(),
    ),
    GetPage(
      name: menu,
      page: () => const MenuScreen(),
    ),
    GetPage(
      name: cart,
      page: () => const CartScreen(),
    ),
    GetPage(
      name: checkout,
      page: () => const CheckoutScreen(),
    ),
    GetPage(
      name: orders,
      page: () => const OrdersScreen(),
    ),
    GetPage(
      name: orderDetails,
      page: () => const OrderDetailsScreen(),
    ),
    GetPage(
      name: admin,
      page: () => const AdminScreen(),
    ),
    GetPage(
      name: driver,
      page: () => const DriverScreen(),
    ),
    // Restaurant routes
    GetPage(
      name: restaurantDashboard,
      page: () => const RestaurantDashboard(),
    ),
    GetPage(
      name: menuManagement,
      page: () => const MenuManagementScreen(),
    ),
    GetPage(
      name: orderManagement,
      page: () => const OrderManagementScreen(),
    ),
    GetPage(
      name: restaurantProfile,
      page: () => const RestaurantProfileScreen(),
    ),
    // Driver routes
    GetPage(
      name: driverDashboard,
      page: () => const DriverDashboard(),
    ),
    GetPage(
      name: driverEarnings,
      page: () => const DriverEarningsScreen(),
    ),
    GetPage(
      name: driverProfile,
      page: () => const DriverProfileScreen(),
    ),
    // Admin routes
    GetPage(
      name: adminDashboard,
      page: () => const AdminDashboard(),
    ),
    GetPage(
      name: userManagement,
      page: () => const UserManagementScreen(),
    ),
    GetPage(
      name: analytics,
      page: () => const AnalyticsScreen(),
    ),
    // Navigation routes
    GetPage(
      name: mainNavigation,
      page: () => const MainNavigation(),
    ),
  ];
}

// Placeholder screens - these will be created later

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(
        body: Center(child: Text('Profile Screen')),
      );
}

class RestaurantScreen extends StatelessWidget {
  const RestaurantScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(
        body: Center(child: Text('Restaurant Screen')),
      );
}

class MenuScreen extends StatelessWidget {
  const MenuScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(
        body: Center(child: Text('Menu Screen')),
      );
}

class CartScreen extends StatelessWidget {
  const CartScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(
        body: Center(child: Text('Cart Screen')),
      );
}

class CheckoutScreen extends StatelessWidget {
  const CheckoutScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(
        body: Center(child: Text('Checkout Screen')),
      );
}

class OrdersScreen extends StatelessWidget {
  const OrdersScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(
        body: Center(child: Text('Orders Screen')),
      );
}

class OrderDetailsScreen extends StatelessWidget {
  const OrderDetailsScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(
        body: Center(child: Text('Order Details Screen')),
      );
}

class AdminScreen extends StatelessWidget {
  const AdminScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(
        body: Center(child: Text('Admin Screen')),
      );
}

class DriverScreen extends StatelessWidget {
  const DriverScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(
        body: Center(child: Text('Driver Screen')),
      );
}
