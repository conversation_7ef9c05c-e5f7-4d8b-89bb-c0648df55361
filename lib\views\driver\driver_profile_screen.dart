import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/user_controller.dart';
import '../../models/user.dart';
import '../shared/custom_app_bar.dart';

import '../shared/custom_buttons.dart';
import '../shared/loading_widget.dart';
import '../shared/snackbars.dart';

class DriverProfileScreen extends StatefulWidget {
  const DriverProfileScreen({super.key});

  @override
  State<DriverProfileScreen> createState() => _DriverProfileScreenState();
}

class _DriverProfileScreenState extends State<DriverProfileScreen> {
  final UserController _userController = Get.find<UserController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: const CustomAppBar(
        title: 'Driver Profile',
      ),
      body: Obx(() {
        final driver = _userController.currentUser;
        if (driver == null) {
          return const LoadingWidget(message: 'Loading profile...');
        }

        return SingleChildScrollView(
          child: Column(
            children: [
              // Driver Header
              _buildDriverHeader(driver),
              const SizedBox(height: 16),
              // Driver Stats
              _buildDriverStats(driver),
              const SizedBox(height: 16),
              // Vehicle Information
              _buildVehicleInformation(),
              const SizedBox(height: 16),
              // Documents
              _buildDocuments(),
              const SizedBox(height: 16),
              // Settings
              _buildSettings(),
              const SizedBox(height: 32),
              // Logout Button
              _buildLogoutButton(),
              const SizedBox(height: 32),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildDriverHeader(UserModel driver) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Driver Photo
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(50),
            ),
            child: driver.profileImageUrl != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(50),
                    child: Image.network(
                      driver.profileImageUrl!,
                      fit: BoxFit.cover,
                    ),
                  )
                : Icon(
                    Icons.person,
                    size: 50,
                    color: Theme.of(context).primaryColor,
                  ),
          ),
          const SizedBox(height: 16),
          // Driver Name
          Text(
            driver.fullName,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          // Driver Email
          Text(
            driver.email,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          // Driver Phone
          Text(
            driver.phone ?? 'No phone number',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 16),
          // Status Badge
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Text(
                  'ACTIVE DRIVER',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.blue,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.star, color: Colors.white, size: 16),
                    const SizedBox(width: 4),
                    Text(
                      driver.stats.rate.rating.toStringAsFixed(1),
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDriverStats(UserModel driver) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Driver Statistics',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'Total Deliveries',
                  '${driver.stats.totalOrders}',
                  Icons.delivery_dining,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  'Rating',
                  driver.stats.rate.rating.toStringAsFixed(1),
                  Icons.star,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'Total Earnings',
                  '\$${driver.stats.totalRevenue.toStringAsFixed(0)}',
                  Icons.attach_money,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  'Distance Driven',
                  '${(driver.stats.totalRevenue / 10).toStringAsFixed(0)} km',
                  Icons.route,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: Theme.of(context).primaryColor,
          size: 24,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildVehicleInformation() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          _buildInfoTile(
            icon: Icons.directions_car,
            title: 'Vehicle Information',
            subtitle: 'Honda Civic 2020 • ABC-123',
            onTap: () {
              InfoSnackBar.show('Vehicle info editing coming soon!');
            },
          ),
          _buildDivider(),
          _buildInfoTile(
            icon: Icons.local_gas_station,
            title: 'Vehicle Type',
            subtitle: 'Car • Delivery',
            onTap: () {
              InfoSnackBar.show('Vehicle type editing coming soon!');
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDocuments() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          _buildInfoTile(
            icon: Icons.badge,
            title: 'Driver\'s License',
            subtitle: 'Verified • Expires 12/2025',
            trailing: const Icon(Icons.verified, color: Colors.green),
            onTap: () {
              InfoSnackBar.show('License management coming soon!');
            },
          ),
          _buildDivider(),
          _buildInfoTile(
            icon: Icons.description,
            title: 'Vehicle Registration',
            subtitle: 'Verified • Expires 06/2024',
            trailing: const Icon(Icons.verified, color: Colors.green),
            onTap: () {
              InfoSnackBar.show('Registration management coming soon!');
            },
          ),
          _buildDivider(),
          _buildInfoTile(
            icon: Icons.security,
            title: 'Insurance',
            subtitle: 'Verified • Expires 03/2024',
            trailing: const Icon(Icons.verified, color: Colors.green),
            onTap: () {
              InfoSnackBar.show('Insurance management coming soon!');
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSettings() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          _buildInfoTile(
            icon: Icons.account_balance,
            title: 'Payment Settings',
            subtitle: 'Bank account & tax info',
            onTap: () {
              InfoSnackBar.show('Payment settings coming soon!');
            },
          ),
          _buildDivider(),
          _buildInfoTile(
            icon: Icons.notifications,
            title: 'Notifications',
            subtitle: 'Manage notification preferences',
            onTap: () {
              InfoSnackBar.show('Notification settings coming soon!');
            },
          ),
          _buildDivider(),
          _buildInfoTile(
            icon: Icons.location_on,
            title: 'Location Settings',
            subtitle: 'Delivery zone preferences',
            onTap: () {
              InfoSnackBar.show('Location settings coming soon!');
            },
          ),
          _buildDivider(),
          _buildInfoTile(
            icon: Icons.help,
            title: 'Help & Support',
            subtitle: 'Get help and contact support',
            onTap: () {
              InfoSnackBar.show('Help & support coming soon!');
            },
          ),
        ],
      ),
    );
  }

  Widget _buildInfoTile({
    required IconData icon,
    required String title,
    required String subtitle,
    Widget? trailing,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: Theme.of(context).primaryColor,
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(subtitle),
      trailing: trailing ??
          const Icon(
            Icons.chevron_right,
            color: Colors.grey,
          ),
      onTap: onTap,
    );
  }

  Widget _buildDivider() {
    return Divider(
      height: 1,
      indent: 56,
      color: Colors.grey[200],
    );
  }

  Widget _buildLogoutButton() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: SecondaryButton(
        text: 'Logout',
        onPressed: _handleLogout,
        width: double.infinity,
        textColor: Colors.red,
        borderColor: Colors.red,
      ),
    );
  }

  void _handleLogout() {
    Get.dialog(
      AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              _userController.logout();
            },
            child: const Text(
              'Logout',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}
