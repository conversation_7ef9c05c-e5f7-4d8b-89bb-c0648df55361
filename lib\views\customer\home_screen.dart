import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/user_controller.dart';
import '../../controllers/restaurant_controller.dart';
import '../../controllers/cart_controller.dart';
import '../../core/app_routes.dart';
import '../../models/user.dart';
import '../shared/custom_app_bar.dart';
import '../shared/custom_cards.dart';
import '../shared/loading_widget.dart';
import '../shared/error_widget.dart';
import '../shared/snackbars.dart' as snackbars;
import '../shared/empty_state_widget.dart';

class CustomerHomeScreen extends StatefulWidget {
  const CustomerHomeScreen({super.key});

  @override
  State<CustomerHomeScreen> createState() => _CustomerHomeScreenState();
}

class _CustomerHomeScreenState extends State<CustomerHomeScreen> {
  final UserController _userController = Get.find<UserController>();
  final RestaurantController _restaurantController = Get.find<RestaurantController>();
  final CartController _cartController = Get.find<CartController>();

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    await _restaurantController.loadRestaurants();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: ProfileAppBar(
        title: 'Restaurant Hub',
        userName: _userController.currentUser?.fullName ?? 'User',
        userImageUrl: _userController.currentUser?.profileImageUrl,
        onProfileTap: () => Get.toNamed(AppRoutes.profile),
        actions: [
          Obx(() {
            final itemCount = _cartController.itemCount;
            return Stack(
              children: [
                IconButton(
                  icon: const Icon(Icons.shopping_cart_outlined),
                  onPressed: () => Get.toNamed(AppRoutes.cart),
                ),
                if (itemCount > 0)
                  Positioned(
                    right: 8,
                    top: 8,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 16,
                        minHeight: 16,
                      ),
                      child: Text(
                        '$itemCount',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            );
          }),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadData,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Welcome Section
              _buildWelcomeSection(),
              // Quick Actions
              _buildQuickActions(),
              // Featured Restaurants
              _buildFeaturedRestaurants(),
              // Recent Orders
              _buildRecentOrders(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeSection() {
    final user = _userController.currentUser;
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Hello, ${user?.firstName ?? 'User'}! 👋',
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'What would you like to eat today?',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white70,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(25),
            ),
            child: TextField(
              decoration: InputDecoration(
                hintText: 'Search for restaurants or food...',
                prefixIcon: const Icon(Icons.search),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 12,
                ),
              ),
              onSubmitted: (query) {
                // TODO: Implement search
                snackbars.InfoSnackBar.show('Search feature coming soon!');
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quick Actions',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: ActionCard(
                  title: 'My Orders',
                  subtitle: 'View order history',
                  icon: Icons.receipt_long,
                  onTap: () => Get.toNamed(AppRoutes.orders),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ActionCard(
                  title: 'Cart',
                  subtitle: 'View cart items',
                  icon: Icons.shopping_cart,
                  onTap: () => Get.toNamed(AppRoutes.cart),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturedRestaurants() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Featured Restaurants',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              TextButton(
                onPressed: () {
                  // TODO: Navigate to all restaurants
                  snackbars.InfoSnackBar.show('View all restaurants coming soon!');
                },
                child: const Text('View All'),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Obx(() {
            if (_restaurantController.isLoading) {
              return const LoadingWidget(message: 'Loading restaurants...');
            }

            if (_restaurantController.errorMessage.isNotEmpty) {
              return CustomErrorWidget(
                message: _restaurantController.errorMessage,
                onRetry: _loadData,
              );
            }

            final restaurants = _restaurantController.restaurants;
            if (restaurants.isEmpty) {
              return const EmptyStateWidget(
                title: 'No Restaurants',
                message: 'No restaurants available at the moment.',
                icon: Icons.restaurant,
              );
            }

            return SizedBox(
              height: 200,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: restaurants.length,
                itemBuilder: (context, index) {
                  final restaurant = restaurants[index];
                  return _buildRestaurantCard(restaurant);
                },
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildRestaurantCard(UserModel restaurant) {
    return Container(
      width: 160,
      margin: const EdgeInsets.only(right: 12),
      child: CustomCard(
        onTap: () {
          _restaurantController.selectRestaurant(restaurant);
          Get.toNamed(AppRoutes.menu);
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: 80,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Center(
                child: Icon(
                  Icons.restaurant,
                  size: 40,
                  color: Colors.grey,
                ),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              restaurant.fullName,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                const Icon(Icons.star, size: 14, color: Colors.orange),
                const SizedBox(width: 4),
                Text(
                  restaurant.stats.rate.rating.toStringAsFixed(1),
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              restaurant.stats.address.estimatedDelivery,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentOrders() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Recent Orders',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 12),
          // TODO: Implement recent orders from OrderController
          const EmptyStateWidget(
            title: 'No Recent Orders',
            message: 'You haven\'t placed any orders yet.',
            icon: Icons.receipt_long,
            actionText: 'Browse Restaurants',
          ),
        ],
      ),
    );
  }
}
