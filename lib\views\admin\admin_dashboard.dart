import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/user_controller.dart';
import '../../core/app_routes.dart';
import '../shared/custom_app_bar.dart';
import '../shared/custom_cards.dart';
import '../shared/error_widget.dart';

class AdminDashboard extends StatefulWidget {
  const AdminDashboard({super.key});

  @override
  State<AdminDashboard> createState() => _AdminDashboardState();
}

class _AdminDashboardState extends State<AdminDashboard> {
  final UserController _userController = Get.find<UserController>();

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    // TODO: Implement actual data loading
    // await Future.wait([
    //   _restaurantController.loadAllRestaurants(),
    //   _orderController.loadAllOrders(),
    //   _userController.loadAllUsers(),
    // ]);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: ProfileAppBar(
        title: 'Admin Dashboard',
        userName: _userController.currentUser?.fullName ?? 'Admin',
        userImageUrl: _userController.currentUser?.profileImageUrl,
        onProfileTap: () => Get.toNamed(AppRoutes.profile),
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {
              InfoSnackBar.show(context, 'Notifications coming soon!');
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadDashboardData,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // System Overview
              _buildSystemOverview(),
              // Platform Stats
              _buildPlatformStats(),
              // Recent Activity
              _buildRecentActivity(),
              // Quick Actions
              _buildQuickActions(),
              // System Health
              _buildSystemHealth(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSystemOverview() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Restaurant Hub Admin',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'System Status: All Services Operational',
            style: TextStyle(
              fontSize: 14,
              color: Colors.white.withOpacity(0.9),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildOverviewItem(
                  'Total Users',
                  '1,234',
                  Icons.people,
                ),
              ),
              Expanded(
                child: _buildOverviewItem(
                  'Active Orders',
                  '89',
                  Icons.receipt_long,
                ),
              ),
              Expanded(
                child: _buildOverviewItem(
                  'Restaurants',
                  '156',
                  Icons.restaurant,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Colors.white, size: 24),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.white70,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildPlatformStats() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Platform Statistics',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: StatCard(
                  title: 'Revenue',
                  value: '\$45.2K',
                  icon: Icons.attach_money,
                  color: Colors.green,
                  subtitle: '+12% this month',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: StatCard(
                  title: 'Orders',
                  value: '2,847',
                  icon: Icons.shopping_cart,
                  color: Colors.blue,
                  subtitle: '+8% this week',
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: StatCard(
                  title: 'New Users',
                  value: '234',
                  icon: Icons.person_add,
                  color: Colors.purple,
                  subtitle: 'This week',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: StatCard(
                  title: 'Avg Rating',
                  value: '4.6',
                  icon: Icons.star,
                  color: Colors.orange,
                  subtitle: 'Platform average',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRecentActivity() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recent Activity',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              TextButton(
                onPressed: () {
                  InfoSnackBar.show(context, 'Activity log coming soon!');
                },
                child: const Text('View All'),
              ),
            ],
          ),
          const SizedBox(height: 12),
          CustomCard(
            child: Column(
              children: [
                _buildActivityItem(
                  'New restaurant registered',
                  'Pizza Palace joined the platform',
                  Icons.restaurant,
                  Colors.green,
                  '2 hours ago',
                ),
                _buildDivider(),
                _buildActivityItem(
                  'High order volume alert',
                  'McDonald\'s received 50+ orders in 1 hour',
                  Icons.trending_up,
                  Colors.orange,
                  '4 hours ago',
                ),
                _buildDivider(),
                _buildActivityItem(
                  'Driver verification completed',
                  'John Smith completed document verification',
                  Icons.verified_user,
                  Colors.blue,
                  '6 hours ago',
                ),
                _buildDivider(),
                _buildActivityItem(
                  'System maintenance completed',
                  'Payment gateway maintenance finished',
                  Icons.build,
                  Colors.grey,
                  '1 day ago',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActivityItem(
    String title,
    String subtitle,
    IconData icon,
    Color color,
    String time,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  subtitle,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Text(
            time,
            style: TextStyle(
              color: Colors.grey[500],
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quick Actions',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: ActionCard(
                  title: 'User Management',
                  subtitle: 'Manage users & roles',
                  icon: Icons.people,
                  onTap: () {
                    InfoSnackBar.show(context, 'User management coming soon!');
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ActionCard(
                  title: 'Restaurant Management',
                  subtitle: 'Approve & manage restaurants',
                  icon: Icons.restaurant,
                  onTap: () {
                    InfoSnackBar.show(context, 'Restaurant management coming soon!');
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: ActionCard(
                  title: 'Analytics',
                  subtitle: 'View detailed reports',
                  icon: Icons.analytics,
                  onTap: () {
                    InfoSnackBar.show(context, 'Analytics coming soon!');
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ActionCard(
                  title: 'System Settings',
                  subtitle: 'Configure platform',
                  icon: Icons.settings,
                  onTap: () {
                    InfoSnackBar.show(context, 'System settings coming soon!');
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSystemHealth() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'System Health',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 12),
          CustomCard(
            child: Column(
              children: [
                _buildHealthItem('API Server', 'Operational', Colors.green),
                _buildDivider(),
                _buildHealthItem('Database', 'Operational', Colors.green),
                _buildDivider(),
                _buildHealthItem('Payment Gateway', 'Operational', Colors.green),
                _buildDivider(),
                _buildHealthItem('Notification Service', 'Operational', Colors.green),
                _buildDivider(),
                _buildHealthItem('File Storage', 'Operational', Colors.green),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHealthItem(String service, String status, Color statusColor) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            service,
            style: const TextStyle(
              fontWeight: FontWeight.w500,
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              status,
              style: TextStyle(
                color: statusColor,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDivider() {
    return Divider(
      height: 1,
      color: Colors.grey[200],
    );
  }
}
