import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/user_controller.dart';
import '../shared/custom_app_bar.dart';
import '../shared/custom_cards.dart';
import '../shared/custom_buttons.dart';
import '../shared/snackbars.dart';

class AdminSettingsScreen extends StatefulWidget {
  const AdminSettingsScreen({super.key});

  @override
  State<AdminSettingsScreen> createState() => _AdminSettingsScreenState();
}

class _AdminSettingsScreenState extends State<AdminSettingsScreen> {
  final UserController _userController = Get.find<UserController>();

  // Settings state
  bool _emailNotifications = true;
  bool _pushNotifications = true;
  bool _maintenanceMode = false;
  bool _allowNewRegistrations = true;
  bool _requireEmailVerification = true;
  String _selectedTheme = 'System';
  String _selectedLanguage = 'English';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: const CustomAppBar(
        title: 'Admin Settings',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSystemSettings(),
            const SizedBox(height: 24),
            _buildNotificationSettings(),
            const SizedBox(height: 24),
            _buildAppearanceSettings(),
            const SizedBox(height: 24),
            _buildSecuritySettings(),
            const SizedBox(height: 24),
            _buildDataManagement(),
            const SizedBox(height: 24),
            _buildDangerZone(),
          ],
        ),
      ),
    );
  }

  Widget _buildSystemSettings() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'System Settings',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildSwitchTile(
            title: 'Maintenance Mode',
            subtitle: 'Temporarily disable the app for maintenance',
            value: _maintenanceMode,
            onChanged: (value) {
              setState(() {
                _maintenanceMode = value;
              });
              _showMaintenanceModeDialog(value);
            },
            icon: Icons.build,
          ),
          _buildSwitchTile(
            title: 'Allow New Registrations',
            subtitle: 'Enable new users to register accounts',
            value: _allowNewRegistrations,
            onChanged: (value) {
              setState(() {
                _allowNewRegistrations = value;
              });
              _saveSettings();
            },
            icon: Icons.person_add,
          ),
          _buildSwitchTile(
            title: 'Require Email Verification',
            subtitle: 'New users must verify their email address',
            value: _requireEmailVerification,
            onChanged: (value) {
              setState(() {
                _requireEmailVerification = value;
              });
              _saveSettings();
            },
            icon: Icons.verified_user,
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationSettings() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Notification Settings',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildSwitchTile(
            title: 'Email Notifications',
            subtitle: 'Receive admin alerts via email',
            value: _emailNotifications,
            onChanged: (value) {
              setState(() {
                _emailNotifications = value;
              });
              _saveSettings();
            },
            icon: Icons.email,
          ),
          _buildSwitchTile(
            title: 'Push Notifications',
            subtitle: 'Receive admin alerts as push notifications',
            value: _pushNotifications,
            onChanged: (value) {
              setState(() {
                _pushNotifications = value;
              });
              _saveSettings();
            },
            icon: Icons.notifications,
          ),
        ],
      ),
    );
  }

  Widget _buildAppearanceSettings() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Appearance',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildDropdownTile(
            title: 'Theme',
            subtitle: 'Choose app theme',
            value: _selectedTheme,
            items: ['Light', 'Dark', 'System'],
            onChanged: (value) {
              setState(() {
                _selectedTheme = value!;
              });
              _saveSettings();
            },
            icon: Icons.palette,
          ),
          _buildDropdownTile(
            title: 'Language',
            subtitle: 'Choose app language',
            value: _selectedLanguage,
            items: ['English', 'Spanish', 'French', 'German'],
            onChanged: (value) {
              setState(() {
                _selectedLanguage = value!;
              });
              _saveSettings();
            },
            icon: Icons.language,
          ),
        ],
      ),
    );
  }

  Widget _buildSecuritySettings() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Security',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildActionTile(
            title: 'Change Password',
            subtitle: 'Update your admin password',
            icon: Icons.lock,
            onTap: _changePassword,
          ),
          _buildActionTile(
            title: 'Two-Factor Authentication',
            subtitle: 'Enable 2FA for enhanced security',
            icon: Icons.security,
            onTap: _setup2FA,
          ),
          _buildActionTile(
            title: 'Active Sessions',
            subtitle: 'View and manage active login sessions',
            icon: Icons.devices,
            onTap: _viewActiveSessions,
          ),
        ],
      ),
    );
  }

  Widget _buildDataManagement() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Data Management',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildActionTile(
            title: 'Export Data',
            subtitle: 'Download system data as CSV/JSON',
            icon: Icons.download,
            onTap: _exportData,
          ),
          _buildActionTile(
            title: 'Import Data',
            subtitle: 'Import data from external sources',
            icon: Icons.upload,
            onTap: _importData,
          ),
          _buildActionTile(
            title: 'Database Backup',
            subtitle: 'Create a backup of the database',
            icon: Icons.backup,
            onTap: _createBackup,
          ),
        ],
      ),
    );
  }

  Widget _buildDangerZone() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Danger Zone',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 16),
          _buildActionTile(
            title: 'Clear Cache',
            subtitle: 'Clear all cached data',
            icon: Icons.clear_all,
            onTap: _clearCache,
            textColor: Colors.orange,
          ),
          _buildActionTile(
            title: 'Reset Settings',
            subtitle: 'Reset all settings to default',
            icon: Icons.restore,
            onTap: _resetSettings,
            textColor: Colors.red,
          ),
        ],
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
    required IconData icon,
  }) {
    return ListTile(
      leading: Icon(icon, color: Theme.of(context).primaryColor),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
      ),
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildDropdownTile({
    required String title,
    required String subtitle,
    required String value,
    required List<String> items,
    required ValueChanged<String?> onChanged,
    required IconData icon,
  }) {
    return ListTile(
      leading: Icon(icon, color: Theme.of(context).primaryColor),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: DropdownButton<String>(
        value: value,
        items: items.map((item) {
          return DropdownMenuItem(
            value: item,
            child: Text(item),
          );
        }).toList(),
        onChanged: onChanged,
      ),
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildActionTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    Color? textColor,
  }) {
    return ListTile(
      leading: Icon(icon, color: textColor ?? Theme.of(context).primaryColor),
      title: Text(
        title,
        style: TextStyle(color: textColor),
      ),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.chevron_right),
      onTap: onTap,
      contentPadding: EdgeInsets.zero,
    );
  }

  void _showMaintenanceModeDialog(bool enable) {
    Get.dialog(
      AlertDialog(
        title: Text(enable ? 'Enable Maintenance Mode' : 'Disable Maintenance Mode'),
        content: Text(
          enable
              ? 'This will temporarily disable the app for all users. Are you sure?'
              : 'This will re-enable the app for all users.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _maintenanceMode = !enable;
              });
              Get.back();
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              _saveSettings();
              SuccessSnackBar.show(
                context,
                enable ? 'Maintenance mode enabled' : 'Maintenance mode disabled',
              );
            },
            child: const Text('Confirm'),
          ),
        ],
      ),
    );
  }

  void _saveSettings() {
    // In a real app, this would save settings to the backend
    SuccessSnackBar.show(context, 'Settings saved successfully');
  }

  void _changePassword() {
    InfoSnackBar.show(context, 'Change password feature coming soon!');
  }

  void _setup2FA() {
    InfoSnackBar.show(context, '2FA setup feature coming soon!');
  }

  void _viewActiveSessions() {
    InfoSnackBar.show(context, 'Active sessions feature coming soon!');
  }

  void _exportData() {
    InfoSnackBar.show(context, 'Data export feature coming soon!');
  }

  void _importData() {
    InfoSnackBar.show(context, 'Data import feature coming soon!');
  }

  void _createBackup() {
    InfoSnackBar.show(context, 'Database backup feature coming soon!');
  }

  void _clearCache() {
    Get.dialog(
      AlertDialog(
        title: const Text('Clear Cache'),
        content: const Text('This will clear all cached data. Continue?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              SuccessSnackBar.show(context, 'Cache cleared successfully');
            },
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  void _resetSettings() {
    Get.dialog(
      AlertDialog(
        title: const Text('Reset Settings'),
        content: const Text('This will reset all settings to default values. This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              setState(() {
                _emailNotifications = true;
                _pushNotifications = true;
                _maintenanceMode = false;
                _allowNewRegistrations = true;
                _requireEmailVerification = true;
                _selectedTheme = 'System';
                _selectedLanguage = 'English';
              });
              SuccessSnackBar.show(context, 'Settings reset to default');
            },
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }
}
