import 'package:get/get.dart';
import '../models/user.dart';
import '../models/menu_item.dart';
import '../services/demo_data_service.dart';

class RestaurantController extends GetxController {
  // Observable variables
  final RxList<UserModel> _restaurants = <UserModel>[].obs;
  final RxList<UserModel> _filteredRestaurants = <UserModel>[].obs;
  final RxList<MenuItem> _allMenuItems = <MenuItem>[].obs;
  final RxList<MenuItem> _filteredMenuItems = <MenuItem>[].obs;
  final Rx<UserModel?> _selectedRestaurant = Rx<UserModel?>(null);
  final RxBool _isLoading = false.obs;
  final RxString _errorMessage = ''.obs;
  final RxString _searchQuery = ''.obs;
  final RxString _selectedCategory = 'All'.obs;
  final RxString _selectedRestaurantFilter = 'All'.obs;
  final RxList<String> _categories = <String>['All'].obs;

  // Getters
  List<UserModel> get restaurants => _filteredRestaurants.isEmpty ? _restaurants : _filteredRestaurants;
  List<MenuItem> get allMenuItems => _allMenuItems;
  List<MenuItem> get filteredMenuItems => _filteredMenuItems;
  UserModel? get selectedRestaurant => _selectedRestaurant.value;
  bool get isLoading => _isLoading.value;
  String get errorMessage => _errorMessage.value;
  String get searchQuery => _searchQuery.value;
  String get selectedCategory => _selectedCategory.value;
  String get selectedRestaurantFilter => _selectedRestaurantFilter.value;
  List<String> get categories => _categories;

  @override
  void onInit() {
    super.onInit();
    _loadDemoData();
    _setupSearchListener();
  }

  // Restaurant management
  Future<void> loadRestaurants() async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 500));

      // In a real app, this would fetch from an API
      // For now, we'll filter users with restaurant role
      _restaurants.clear();
      _restaurants.addAll(_getDemoRestaurants());

      // Initialize filtered restaurants
      _filteredRestaurants.clear();
      _filteredRestaurants.addAll(_restaurants);
    } catch (e) {
      _errorMessage.value = 'Failed to load restaurants: ${e.toString()}';
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> selectRestaurant(UserModel restaurant) async {
    if (restaurant.role != UserRole.restaurant) {
      _errorMessage.value = 'Invalid restaurant selection';
      return;
    }

    _selectedRestaurant.value = restaurant;
    await loadMenuItems(restaurant.id);
  }

  Future<void> loadMenuItems(String restaurantId) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 300));

      // Filter menu items for the selected restaurant
      final restaurantMenuItems =
          _allMenuItems.where((item) => _getRestaurantIdForMenuItem(item.id) == restaurantId).toList();

      _filteredMenuItems.clear();
      _filteredMenuItems.addAll(restaurantMenuItems);

      // Update categories
      _updateCategories();
    } catch (e) {
      _errorMessage.value = 'Failed to load menu items: ${e.toString()}';
    } finally {
      _isLoading.value = false;
    }
  }

  // Menu item management
  Future<bool> addMenuItem(MenuItem menuItem) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 500));

      _allMenuItems.add(menuItem);

      // If this item belongs to the currently selected restaurant, update filtered list
      if (_selectedRestaurant.value != null) {
        await loadMenuItems(_selectedRestaurant.value!.id);
      }

      return true;
    } catch (e) {
      _errorMessage.value = 'Failed to add menu item: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> updateMenuItem(MenuItem updatedItem) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 500));

      final index = _allMenuItems.indexWhere((item) => item.id == updatedItem.id);
      if (index != -1) {
        _allMenuItems[index] = updatedItem;

        // Update filtered list if needed
        final filteredIndex = _filteredMenuItems.indexWhere((item) => item.id == updatedItem.id);
        if (filteredIndex != -1) {
          _filteredMenuItems[filteredIndex] = updatedItem;
        }
      }

      return true;
    } catch (e) {
      _errorMessage.value = 'Failed to update menu item: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> deleteMenuItem(String itemId) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 500));

      _allMenuItems.removeWhere((item) => item.id == itemId);
      _filteredMenuItems.removeWhere((item) => item.id == itemId);

      return true;
    } catch (e) {
      _errorMessage.value = 'Failed to delete menu item: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> toggleMenuItemAvailability(String itemId) async {
    final item = _allMenuItems.firstWhereOrNull((item) => item.id == itemId);
    if (item == null) return false;

    final updatedItem = MenuItem(
      id: item.id,
      name: item.name,
      description: item.description,
      price: item.price,
      image: item.image,
      category: item.category,
      isAvailable: !item.isAvailable,
      allergens: item.allergens,
    );

    return await updateMenuItem(updatedItem);
  }

  // Search and filtering
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  void setSelectedCategory(String category) {
    _selectedCategory.value = category;
    _applyFilters();
  }

  void clearFilters() {
    _searchQuery.value = '';
    _selectedCategory.value = 'All';
    _applyFilters();
  }

  // Restaurant operations (for restaurant owners)
  Future<bool> updateRestaurantInfo(UserModel updatedRestaurant) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 500));

      final index = _restaurants.indexWhere((r) => r.id == updatedRestaurant.id);
      if (index != -1) {
        _restaurants[index] = updatedRestaurant;

        if (_selectedRestaurant.value?.id == updatedRestaurant.id) {
          _selectedRestaurant.value = updatedRestaurant;
        }
      }

      return true;
    } catch (e) {
      _errorMessage.value = 'Failed to update restaurant info: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<List<MenuItem>> getRestaurantMenu(String restaurantId) async {
    return _allMenuItems.where((item) => _getRestaurantIdForMenuItem(item.id) == restaurantId).toList();
  }

  // Analytics and statistics
  Map<String, dynamic> getRestaurantStats(String restaurantId) {
    final menuItems = _allMenuItems.where((item) => _getRestaurantIdForMenuItem(item.id) == restaurantId).toList();

    final availableItems = menuItems.where((item) => item.isAvailable).length;
    final totalItems = menuItems.length;
    final categories = menuItems.map((item) => item.category).toSet().length;

    return {
      'totalMenuItems': totalItems,
      'availableItems': availableItems,
      'unavailableItems': totalItems - availableItems,
      'categories': categories,
      'averagePrice':
          menuItems.isEmpty ? 0.0 : menuItems.map((item) => item.price).reduce((a, b) => a + b) / totalItems,
    };
  }

  // Private methods
  void _setupSearchListener() {
    // Listen to search query changes and apply filters
    ever(_searchQuery, (_) => _applyFilters());
    ever(_selectedCategory, (_) => _applyFilters());
  }

  void _applyFilters() {
    if (_selectedRestaurant.value == null) return;

    var items =
        _allMenuItems.where((item) => _getRestaurantIdForMenuItem(item.id) == _selectedRestaurant.value!.id).toList();

    // Apply search filter
    if (_searchQuery.value.isNotEmpty) {
      items = items
          .where((item) =>
              item.name.toLowerCase().contains(_searchQuery.value.toLowerCase()) ||
              item.description.toLowerCase().contains(_searchQuery.value.toLowerCase()) ||
              item.category.toLowerCase().contains(_searchQuery.value.toLowerCase()))
          .toList();
    }

    // Apply category filter
    if (_selectedCategory.value != 'All') {
      items = items.where((item) => item.category == _selectedCategory.value).toList();
    }

    _filteredMenuItems.clear();
    _filteredMenuItems.addAll(items);
  }

  void _updateCategories() {
    final categorySet = <String>{'All'};
    for (final item in _filteredMenuItems) {
      categorySet.add(item.category);
    }
    _categories.clear();
    _categories.addAll(categorySet.toList()..sort());
  }

  Future<void> _loadDemoData() async {
    try {
      // Load demo data from JSON file
      await DemoDataService.instance.loadDemoData();

      // Load restaurants (users with restaurant role)
      final restaurants = DemoDataService.instance.getUsersByRole(UserRole.restaurant);
      _restaurants.addAll(restaurants);

      // Load menu items
      final menuItems = DemoDataService.instance.getMenuItems();
      _allMenuItems.addAll(menuItems);

      // Update categories
      _updateCategories();
    } catch (e) {
      // Fallback to hardcoded demo data if JSON loading fails
      _restaurants.addAll(_getDemoRestaurants());
      _allMenuItems.addAll(_getDemoMenuItems());
    }
  }

  List<UserModel> _getDemoRestaurants() {
    return [
      UserModel(
        id: 'rest_001',
        firstName: "Tony's",
        lastName: 'Pizzeria',
        email: '<EMAIL>',
        phone: '+1555987654',
        dateJoined: DateTime.now().subtract(const Duration(days: 100)),
        role: UserRole.restaurant,
        isEmailVerified: true,
        preferences: UserPreferences(),
        stats: UserStats(
          rate: RateModel(
            id: 'rate_rest_001',
            userId: 'rest_001',
            restaureantId: 'rest_001',
            rating: 4.7,
            comment: 'Highly rated restaurant',
            createdAt: DateTime.now(),
          ),
          address: AddressModel(
            id: 'addr_rest_001',
            type: 'Restaurant',
            fullAddress: '321 Food Street, New York, NY 10004',
            unit: null,
            isDefault: true,
            estimatedDelivery: '30-45 mins',
          ),
        ),
      ),
      UserModel(
        id: 'rest_002',
        firstName: 'Burger',
        lastName: 'Palace',
        email: '<EMAIL>',
        phone: '+1555456789',
        dateJoined: DateTime.now().subtract(const Duration(days: 80)),
        role: UserRole.restaurant,
        isEmailVerified: true,
        preferences: UserPreferences(),
        stats: UserStats(
          rate: RateModel(
            id: 'rate_rest_002',
            userId: 'rest_002',
            restaureantId: 'rest_002',
            rating: 4.3,
            comment: 'Great burgers and fries',
            createdAt: DateTime.now(),
          ),
          address: AddressModel(
            id: 'addr_rest_002',
            type: 'Restaurant',
            fullAddress: '654 Burger Lane, New York, NY 10005',
            unit: null,
            isDefault: true,
            estimatedDelivery: '25-40 mins',
          ),
        ),
      ),
    ];
  }

  List<MenuItem> _getDemoMenuItems() {
    return [
      // Tony's Pizzeria items
      MenuItem(
        id: 'menu_001',
        name: 'Margherita Pizza',
        description: 'Classic pizza with fresh mozzarella, tomato sauce, and basil',
        price: 18.99,
        image: 'https://example.com/menu/margherita_pizza.jpg',
        category: 'Pizza',
        isAvailable: true,
        allergens: ['gluten', 'dairy'],
      ),
      MenuItem(
        id: 'menu_002',
        name: 'Pepperoni Pizza',
        description: 'Traditional pepperoni pizza with mozzarella cheese',
        price: 21.99,
        image: 'https://example.com/menu/pepperoni_pizza.jpg',
        category: 'Pizza',
        isAvailable: true,
        allergens: ['gluten', 'dairy'],
      ),
      MenuItem(
        id: 'menu_003',
        name: 'Caesar Salad',
        description: 'Fresh romaine lettuce with Caesar dressing, croutons, and parmesan',
        price: 12.99,
        image: 'https://example.com/menu/caesar_salad.jpg',
        category: 'Salads',
        isAvailable: true,
        allergens: ['dairy', 'eggs'],
      ),
      // Burger Palace items
      MenuItem(
        id: 'menu_004',
        name: 'Classic Cheeseburger',
        description: 'Beef patty with cheese, lettuce, tomato, and special sauce',
        price: 15.99,
        image: 'https://example.com/menu/cheeseburger.jpg',
        category: 'Burgers',
        isAvailable: true,
        allergens: ['gluten', 'dairy'],
      ),
      MenuItem(
        id: 'menu_005',
        name: 'BBQ Bacon Burger',
        description: 'Beef patty with BBQ sauce, bacon, onion rings, and cheese',
        price: 18.99,
        image: 'https://example.com/menu/bbq_bacon_burger.jpg',
        category: 'Burgers',
        isAvailable: true,
        allergens: ['gluten', 'dairy'],
      ),
      MenuItem(
        id: 'menu_006',
        name: 'Sweet Potato Fries',
        description: 'Crispy sweet potato fries with sea salt',
        price: 7.99,
        image: 'https://example.com/menu/sweet_potato_fries.jpg',
        category: 'Sides',
        isAvailable: true,
        allergens: [],
      ),
    ];
  }

  String _getRestaurantIdForMenuItem(String menuItemId) {
    // Simple mapping for demo purposes
    // In a real app, this would be stored in the menu item model
    if (['menu_001', 'menu_002', 'menu_003'].contains(menuItemId)) {
      return 'rest_001'; // Tony's Pizzeria
    } else if (['menu_004', 'menu_005', 'menu_006'].contains(menuItemId)) {
      return 'rest_002'; // Burger Palace
    }
    return '';
  }

  // Utility methods
  void clearError() {
    _errorMessage.value = '';
  }

  MenuItem? getMenuItemById(String itemId) {
    return _allMenuItems.firstWhereOrNull((item) => item.id == itemId);
  }

  List<MenuItem> getMenuItemsByCategory(String category) {
    if (category == 'All') return _filteredMenuItems;
    return _filteredMenuItems.where((item) => item.category == category).toList();
  }

  double getAverageRating(String restaurantId) {
    final restaurant = _restaurants.firstWhereOrNull((r) => r.id == restaurantId);
    return restaurant?.stats.rate.rating ?? 0.0;
  }

  // Search functionality
  void searchRestaurants(String query) {
    _searchQuery.value = query;
    _applyRestaurantFilters();
  }

  void clearSearch() {
    _searchQuery.value = '';
    _applyRestaurantFilters();
  }

  void _applyRestaurantFilters() {
    var restaurants = List<UserModel>.from(_restaurants);

    // Apply category filter first
    if (_selectedRestaurantFilter.value != 'All') {
      restaurants = restaurants
          .where((restaurant) => _getRestaurantCuisineType(restaurant) == _selectedRestaurantFilter.value)
          .toList();
    }

    // Apply search filter
    if (_searchQuery.value.isNotEmpty) {
      final query = _searchQuery.value.toLowerCase();
      restaurants = restaurants
          .where((restaurant) =>
              restaurant.firstName.toLowerCase().contains(query) ||
              restaurant.lastName.toLowerCase().contains(query) ||
              '${restaurant.firstName} ${restaurant.lastName}'.toLowerCase().contains(query) ||
              restaurant.stats.address.fullAddress.toLowerCase().contains(query))
          .toList();
    }

    _filteredRestaurants.assignAll(restaurants);
  }

  // Filter by category
  void filterByCategory(String category) {
    _selectedCategory.value = category;
    if (category == 'All') {
      _filteredMenuItems.assignAll(_allMenuItems);
    } else {
      _filteredMenuItems.assignAll(
        _allMenuItems.where((item) => item.category == category).toList(),
      );
    }
  }

  // Filter restaurants by category/cuisine type
  void filterRestaurantsByCategory(String category) {
    _selectedRestaurantFilter.value = category;
    _applyRestaurantFilters();
  }

  // Helper method to determine restaurant cuisine type based on name/description
  String _getRestaurantCuisineType(UserModel restaurant) {
    final name = '${restaurant.firstName} ${restaurant.lastName}'.toLowerCase();

    if (name.contains('pizza') || name.contains('pizzeria')) {
      return 'Pizza';
    } else if (name.contains('burger') || name.contains('fast')) {
      return 'Fast Food';
    } else if (name.contains('asian') || name.contains('chinese') || name.contains('thai') || name.contains('sushi')) {
      return 'Asian';
    } else if (name.contains('italian') || name.contains('pasta')) {
      return 'Italian';
    } else if (name.contains('mexican') || name.contains('taco') || name.contains('burrito')) {
      return 'Mexican';
    }

    // Default to Fast Food for demo restaurants
    return 'Fast Food';
  }

  // Category management
  Future<bool> addCategory(String categoryName) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Validate category name
      if (categoryName.trim().isEmpty) {
        _errorMessage.value = 'Category name cannot be empty';
        return false;
      }

      final trimmedName = categoryName.trim();

      // Check if category already exists
      if (_categories.contains(trimmedName)) {
        _errorMessage.value = 'Category already exists';
        return false;
      }

      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 300));

      // Add category to the list
      _categories.add(trimmedName);
      _categories.sort();

      return true;
    } catch (e) {
      _errorMessage.value = 'Failed to add category: ${e.toString()}';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }
}
