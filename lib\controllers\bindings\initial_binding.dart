import 'package:get/get.dart';
import '../user_controller.dart';
import '../restaurant_controller.dart';
import '../cart_controller.dart';
import '../order_controller.dart';
import '../payment_controller.dart';
import '../admin_controller.dart';
import '../driver_controller.dart';

class InitialBinding extends Bindings {
  @override
  void dependencies() {
    // Initialize all controllers as lazy singletons
    Get.lazyPut<UserController>(() => UserController(), fenix: true);
    Get.lazyPut<RestaurantController>(() => RestaurantController(), fenix: true);
    Get.lazyPut<CartController>(() => CartController(), fenix: true);
    Get.lazyPut<OrderController>(() => OrderController(), fenix: true);
    Get.lazyPut<PaymentController>(() => PaymentController(), fenix: true);
    Get.lazyPut<AdminController>(() => AdminController(), fenix: true);
    Get.lazyPut<DriverController>(() => DriverController(), fenix: true);
  }
}
